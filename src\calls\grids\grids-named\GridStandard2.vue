<template>
  <div :id="gridController.targetGridContainerId">
    <!--grid-header-->
    <div class="grid--header">
      <!--      :view-title="gridDefinition.title"-->
      <GridRouteToolbar
        :call-count="gridController.state.callGridCount"
        :call-filter-count="gridController.state.callFilterCount"
        :is-loading="gridController.state.isLoading"
        :last-reloaded="gridController.state.lastUpdatedHumanReadTime"
        :socket-status="gridController.cleoSocketWrapperController.status"
        :socket-loading-trigger="gridController.state.socketLoadingTrigger"
        :are-any-filters-active="gridController.areAnyFiltersActive.value"
        v-on:getData="gridController.getDataFromLegacyServer()"
        v-on:showSocketMessages="showSocketMessages"
      >
        <div slot="toolbar-content">
          <div
            class="ic24-flex-row ic24-flex-gap ic24-justify-fle-row-vert-center"
          >
            <!--            Simple proof of concept to show dynamic view switching.-->
            <!--            <select-->
            <!--              class="ic24-input"-->
            <!--              v-model="routeSelector"-->
            <!--              @change="userRouteChanged"-->
            <!--            >-->
            <!--              <option value="grid-Cas">CAS Queue</option>-->
            <!--              <option value="grid-oversight">Oversight</option>-->
            <!--              <option value="grid-oversight-follow-up"-->
            <!--                >Oversight Follow Up</option-->
            <!--              >-->
            <!--              <option value="grid-classification-base">Face2Face</option>-->
            <!--              <option value="grid-classification-visit">Home Visit</option>-->
            <!--            </select>-->

            <ToolbarWidgetNewCall
              :user-app-perms-short="permissionStoreState.userAppPermsShort"
            />

            <!--            <ToolbarWidgetActions-->
            <!--              :user-app-perms-short="permissionStoreState.userAppPermsShort"-->
            <!--            />-->

            <InputDebounce
              :place-holder="'Enter search term...'"
              v-model="quickFilterText"
              class="ic24-input"
              :class="
                quickFilterText.length > 0
                  ? 'grid-route-toolbar--filters-warn-value'
                  : ''
              "
              @onChange="quickFilterTextChanged"
            />

            <Ic24Button
              title="Filters"
              @click="gridController.state.showFilterDialog = true"
            />

            <span v-if="gridController.state.filters.length > 0"
              >Filters Applied</span
            >

            <GridFilterCov19
              v-if="false"
              class="grid-route-toolbar--filters-sep"
              style="display: inline"
              v-on:change="gridController.onCov19Filter"
            />

            <GridFilterClassn
              v-if="false"
              v-show="
                permissionStoreState.userAppPermsShort
                  .GRID_FILTER_CLASSIFICATION
              "
              class="grid-route-toolbar--filters-sep"
              style="display: inline"
              :classifications="getClassificationFilterOptions"
              v-on:change="gridController.onClassificationFilter"
            />

            <button
              v-on:click="startSocketTest"
              v-if="
                permissionStoreState.userAppPermsShort[
                  permissionNames.GRID__INT_TEST
                ]
              "
              style="color: red;font-weight: 600;margin-left: 5px;"
            >
              Test
            </button>
          </div>
        </div>
      </GridRouteToolbar>
    </div>
    <!--    /grid-header-->

    <CleoModalV2
      v-show="gridController.state.showFilterDialog"
      :title="'Filter View Options' + ' - ' + gridDefinition.title"
    >
      <ButtonFilters
        slot="content"
        style="padding: var(--ic24-flex-gap)"
        :grid-filter-user-input="gridController.state.filterUserInput"
        :apply-filters-immediately="false"
        :grid-id="gridDefinition.identifier"
        @input="filterDialogClosed"
        @cancel="gridController.state.showFilterDialog = false"
      />
      <div slot="buttons"></div>
    </CleoModalV2>

    <GridDefault
      oncontextmenu="javascript:return false;"
      :grid-definition="gridDefinition"
      :grid-data="gridController.state.gridData"
      :new-grid-data="gridController.state.newCalls"
      :updated-grid-data="gridController.state.updatedCalls"
      :removed-grid-data="gridController.state.removedCalls"
      :quick-filter-trigger="gridController.state.quickFilterText"
      :recalc-breach-trigger="gridController.state.simpleTriggerBreach"
      :show-multi-select="allowMultiSelect"
      :external-filters="gridController.state.filters"
      :export-grid-data-trigger="exportGridDataTrigger"
      v-on:onRowClicked="gridController.onRowClicked"
      v-on:onRowDoubleClicked="gridController.onRowDoubleClicked"
      v-on:onCellContextMenu="gridController.onCellContextMenu"
      v-on:currentGridRowCount="gridController.state.callGridCount === $event"
      v-on:onFilterChangedRowCount="setFilterCount"
      v-on:exportGridData="gridController.setExportGridData"
    />

    <!--    v-if="false"-->
    <!--    <CleoContextMenu2-->
    <!--      v-if="false"-->
    <!--      :show-menu="gridController.state.rightClick.showMenu"-->
    <!--      :contain-with-dom-id="gridController.targetGridContainerId"-->
    <!--      :perms-loading="gridController.state.isLoadingPermsForSelectedCall"-->
    <!--      :perms-for-call="gridController.state.permsForSelectedCall"-->
    <!--      :cleo-call-summary="gridController.cleoCallSummarySelected.value"-->
    <!--      :clicked-element-coords="gridController.state.rightClick.coords"-->
    <!--      v-on:hideMenu="gridController.state.rightClick.showMenu = false"-->
    <!--    />-->

    <CleoModal
      v-if="gridController.showConfirmOpeningCall.value"
      header-message="Open Call"
      :body-message="gridController.getOpenCallMessage.value"
      v-on:closeSecondary="gridController.showConfirmOpeningCall.value = false"
      v-on:closePrimary="gridController.proceedToOpeningLockedCall"
    />

    <CleoModal
      v-if="gridController.state.showStackMessages"
      :header-message="
        gridDefinition.title +
          ': Last ' +
          gridControllerAppConfig.stackMessagesMaxToShow +
          ' Socket Messages'
      "
      v-on:closePrimary="gridController.state.showStackMessages = false"
    >
      <div slot="button-close-secondary"></div>
      <GridControllerMessages
        slot="body"
        style="height: 400px;overflow: auto;"
        :grid-controller-stack-messages="gridController.state.stackMessages"
      />
    </CleoModal>

    <CleoModal
      v-if="showSocketTest"
      header-message="Socket Test"
      css-dialog-style="width: 500px;"
      v-on:closePrimary="showSocketTest = false"
    >
      <div slot="button-close-secondary"></div>
      <GridIntegrationTest
        slot="body"
        :adapter-calls="gridController.state.gridDataExported"
        :grid-definition="gridDefinition"
      />
    </CleoModal>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  onBeforeUnmount,
  PropType,
  ref,
  SetupContext,
  watch
} from "@vue/composition-api";
import { IGridDefinition } from "../grid-models";

import { useStore } from "@/store/store";
import GridRouteToolbar from "@/calls/grids/grid-route-toolbar.vue";
import {
  IGridControllerAppConfig,
  useGridController
} from "@/calls/grids/grids-named/useGridController";
import { ILoginStoreState, LOGIN_STORE_STORE_CONST } from "@/login/login-store";
import GridDefault from "@/calls/grids/grid-default.vue";
import ToolbarWidgetNewCall from "@/calls/grids/toolbar-widgets/toolbar-widget-newcall.vue";
import {
  IPermissionStoreState,
  PERMISSION_STORE_CONST
} from "@/permissions/permisssion-store";
import { PERMISSION_NAMES } from "@/permissions/permission-models";
import GridFilterCov19 from "@/calls/grids/grid-filter/grid-filter-cov19.vue";
import GridFilterClassn from "@/calls/grids/grid-filter/grid-filter-classn.vue";
import { CALL_CLASSIFICATION } from "@/common/common-models";
import GridIntegrationTest from "@/calls/grids/grids-named/integration-test/GridIntegrationTest.vue";
import CleoModal from "@/common/ui/modal/cleo-modal.vue";
import { IUserStoreState, USER_STORE_CONST } from "@/user/user-store";
import GridControllerMessages from "@/calls/grids/grid-controller/grid-controller-messages.vue";
import {
  ICleoCallSummary,
  ISimpleTrigger
} from "@/calls/summary/call-summarry-models";
import ButtonFilters from "@/calls/grids/grid-filter/generic-filters/ui/ButtonFilters.vue";
import CleoModalV2 from "@/common/ui/modal/cleo-model-v2.vue";
import Ic24Button from "@/common/ui/buttons/ic24-button.vue";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState
} from "@/common/config/config-store";
import { IAdapterAction } from "@/app-models";
import InputDebounce from "@/common/ui/fields/input-debounce.vue";
import { useRoute, useRouter } from "@/router/migrateRouterVue3";
import { IGridFilterUserInput } from "@/calls/grids/grid-filter/grid-filter-models";
import { useGridFilterState } from "@/calls/grids/grid-filter/useGridFilterState";

export default defineComponent({
  name: "grid-standard2",
  components: {
    InputDebounce,
    Ic24Button,
    CleoModalV2,
    ButtonFilters,
    // CleoContextMenu2,
    GridControllerMessages,
    CleoModal,
    GridRouteToolbar,
    GridDefault,
    GridIntegrationTest,
    GridFilterClassn,
    GridFilterCov19,
    ToolbarWidgetNewCall
  },
  props: {
    gridDefinition: {
      type: Object as PropType<IGridDefinition>,
      required: true
    }
  },
  setup(
    props: { gridDefinition: IGridDefinition; context: SetupContext },
    context: SetupContext
  ) {
    const permissionNames = PERMISSION_NAMES;

    const internalRouter = useRouter();
    const internalRoute = useRoute();

    const routeSelector = ref<string>(internalRoute.name || "");

    const store = useStore();
    const loginStoreState = computed<ILoginStoreState>(() => {
      return store.state[
        LOGIN_STORE_STORE_CONST.LOGIN_STORE__CONST_MODULE_NAME
      ];
    });

    const permissionStoreState = computed<IPermissionStoreState>(() => {
      return store.state[PERMISSION_STORE_CONST.PERMISSION__CONST_MODULE_NAME];
    });

    const userStoreState = computed<IUserStoreState>(() => {
      return store.state[USER_STORE_CONST.USER__CONST_MODULE_NAME];
    });

    const configStoreState = computed<IConfigStoreState>(() => {
      return store.state[CONFIG_STORE_CONST.CONFIG__CONST_MODULE_NAME];
    });

    // RefreshRateMs   longPollingRefreshRateMs
    const gridControllerAppConfig: IGridControllerAppConfig = {
      store,
      headers: {
        Authorization: loginStoreState.value.tokenResponse.access_token,
        HeaderAuthorization: loginStoreState.value.tokenResponse.access_token
      },
      user: userStoreState.value.user,
      longPollingRefreshRateMs:
        window.MyGlobalSession.GRID_LONG_POLLING_SECS * 1000, //  3_000_000
      breachRefreshRateMs:
        window.MyGlobalSession.GRID_BREACH_REFRESH_SECS * 1000,
      stackMessagesMaxToShow: 100
    };

    console.warn(
      ">>> >>> >>> >>> GridStandard2.setup() props.gridDefinition: " +
        props.gridDefinition.title
    );

    const gridController = useGridController(
      context,
      props.gridDefinition,
      gridControllerAppConfig
    );

    // Auto-apply saved filter state if it exists
    const gridFilterState = useGridFilterState();
    const savedFilterState = gridFilterState.getFilterState(
      props.gridDefinition.identifier
    );
    if (savedFilterState) {
      console.log(
        "GridStandard2: Found saved filter state for",
        props.gridDefinition.identifier,
        savedFilterState
      );
      gridController.onGridFilterUserInputChanged(
        savedFilterState.gridFilterUserInput
      );
      console.log(
        "GridStandard2: Applied",
        gridController.state.filters.length,
        "filters automatically"
      );
    }

    const quickFilterText = ref("");

    const getClassificationFilterOptions = computed<CALL_CLASSIFICATION[]>(
      () => {
        //  TODO move to grid definitions...?
        return ["CH Advice", "Message"];
      }
    );

    const allowMultiSelect = computed<boolean>(() => {
      //  TODO needs to come from gridDefinition
      return false;
    });

    function quickFilterTextChanged() {
      gridController.quickFilterTextChanged(quickFilterText.value);
    }

    function setFilterCount(filterCount: number) {
      gridController.state.callFilterCount = filterCount;
    }

    function showSocketMessages(): void {
      gridController.state.showStackMessages = true;
    }

    const showSocketTest = ref(false);
    const exportGridDataTrigger = ref<ISimpleTrigger<string>>({
      timeIso: "",
      data: ""
    });
    function startSocketTest() {
      exportGridDataTrigger.value = { timeIso: new Date().toISOString() };
      showSocketTest.value = true;
    }

    function exportGridData(payload: any) {
      gridController.state.gridDataExported = (payload as any) as ICleoCallSummary[];
    }

    watch(
      () => configStoreState.value.adapterCleoAction,
      (newValue: IAdapterAction, oldValue: IAdapterAction) => {
        console.log("GridStandard2.adapterCleoAction", newValue, oldValue);
        if (newValue.payload.actionType === "REFRESH_GRID") {
          gridController.getDataFromLegacyServer();
        }
      }
    );

    function userRouteChanged() {
      console.log("GridStandard2.useRouteChanged()");
      internalRouter.push({ name: routeSelector.value });
    }

    function filterDialogClosed(gridFilterUserInput: IGridFilterUserInput) {
      const cachedQuickFilterText = quickFilterText.value;
      if (cachedQuickFilterText.length === 0) {
        gridController.filterDialogClosed(gridFilterUserInput);
        return;
      }

      // but if there is some quickfilter text...don't know, but it's like this...
      // you need to clear it, then re-issue.
      quickFilterText.value = "";
      quickFilterTextChanged();
      gridController.filterDialogClosed(gridFilterUserInput);
      quickFilterText.value = cachedQuickFilterText;
      quickFilterTextChanged();
    }

    onBeforeUnmount(() => {
      gridController.destroy();
    });

    return {
      internalRoute,
      routeSelector,
      showSocketTest,
      exportGridDataTrigger,
      userStoreState,
      gridController,
      permissionStoreState,
      permissionNames,
      quickFilterText,
      getClassificationFilterOptions,
      allowMultiSelect,
      gridControllerAppConfig,

      quickFilterTextChanged,
      setFilterCount,
      showSocketMessages,
      startSocketTest,
      exportGridData,
      userRouteChanged,
      filterDialogClosed
    };
  }
});
</script>
