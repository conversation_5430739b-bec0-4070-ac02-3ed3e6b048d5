<template>
  <div class="dat-content-wrapper">
    <div class="dat-flex-column">
      <h3>Se<PERSON> Login</h3>
      <span></span>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, SetupContext } from "@vue/composition-api";

export default defineComponent({
  name: "sesui-state",
  props: {
    defaultUserId: {
      type: String,
      default: () => {
        return "";
      }
    }
  },
  setup(props: { defaultUserId: string }, context: SetupContext) {
    const userId = ref(props.defaultUserId);

    const showCancelConfirm = ref(false);

    return { userId, showCancelConfirm };
  }
});
</script>

<style>
.sesui--wrapper {
  min-width: 400px;
  padding: 10px;
}
</style>
