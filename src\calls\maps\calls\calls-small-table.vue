<template>
  <div class="calls-small-table--wrapper">
    <table>
      <tr>
        <td>Call no</td>
        <td>Breach</td>
        <td>Locate</td>
      </tr>
      <tr
        v-for="cleoCallSummary in cleoCallSummaries"
        :key="cleoCallSummary.CallNo"
      >
        <td><span v-text="cleoCallSummary.CallNo"></span></td>
        <td>
          <span v-text="getBreachTime(cleoCallSummary.BreachActualTime)"></span>
        </td>
        <td>
          <a href="#" v-on:click.prevent="locateCall(cleoCallSummary)"
            >Locate</a
          >
          <a href="#" v-on:click.prevent="routeCall(cleoCallSummary)">Route</a>
        </td>
      </tr>
    </table>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  ref,
  SetupContext
} from "@vue/composition-api";
import { ICleoCallSummary } from "../../summary/call-summarry-models";
import { CallSummaryService } from "@/calls/summary/call-summary-service";
import { format, parseISO } from "date-fns";
import { getMapControllerInstance } from "@/calls/maps/map-controller-factory";

export default defineComponent({
  name: "calls-small-table",
  components: {},
  props: {
    cleoCallSummaries: {
      type: Array as PropType<ICleoCallSummary[]>
    }
  },
  setup(
    props: { cleoCallSummaries: ICleoCallSummary[] },
    context: SetupContext
  ) {
    const callSummaryService = new CallSummaryService();

    function handleClick(cleoCallSummary: ICleoCallSummary) {
      context.emit("cleoCallSummaryClicked", cleoCallSummary);
    }

    function getBreachTime(breachDateTime: string): string {
      console.log(">>>>>>>>> breachDateTime: " + breachDateTime);
      if (breachDateTime && breachDateTime.length > 0) {
        return format(parseISO(breachDateTime), "yyyy MMM do, HH:mm");
      }
      return "";
    }

    function locateCall(cleoCallSummary: ICleoCallSummary) {
      getMapControllerInstance().bounceCallMarker(cleoCallSummary);
    }

    function routeCall(cleoCallSummary: ICleoCallSummary) {
      context.emit("routeCall", cleoCallSummary);
    }

    return {
      handleClick,
      getBreachTime,
      locateCall,
      routeCall
    };
  }
});
</script>

<style scoped>
.calls-small-table--wrapper {
}
</style>
