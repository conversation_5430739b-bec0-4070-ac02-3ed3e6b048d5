export class PafService {
  public isValidPostCode(postCode: string): boolean {
    const regex = new RegExp(
      /^(?:(?:[A-PR-UWYZ][0-9]{1,2}|[A-PR-UWYZ][A-HK-Y][0-9]{1,2}|[A-PR-UWYZ][0-9][A-HJKSTUW]|[A-PR-UWYZ][A-HK-Y][0-9][ABEHMNPRV-Y])[0-9][ABD-HJLNP-UW-Z]{2}|GIR 0AA)$/
    );
    const postCodeTest = postCode.replace(/ /g, "").toUpperCase();
    const answer = regex.test(postCodeTest);
    return answer;
  }
}
