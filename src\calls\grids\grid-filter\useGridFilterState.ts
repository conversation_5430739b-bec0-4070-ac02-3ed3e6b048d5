import { computed } from "@vue/composition-api";
import { useStore } from "@/store/store";
import { SocketGroup } from "@/calls/grids/grid-models";
import { ButtonFiltersControllerState } from "@/calls/grids/grid-filter/generic-filters/models/useButtonFiltersController";
import {
  GRID_FILTER_STATE_STORE_CONST,
  IGridFilterStateStoreState
} from "@/calls/grids/grid-filter/grid-filter-state-store";

export function useGridFilterState() {
  const store = useStore();

  const gridFilterStateStoreState = computed<IGridFilterStateStoreState>(() => {
    return store.state[
      GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME
    ];
  });

  function getFilterState(
    gridId: SocketGroup
  ): ButtonFiltersControllerState | null {
    return store.getters[
      `${GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME}/getFilterState`
    ](gridId);
  }

  function hasFilterState(gridId: SocketGroup): boolean {
    return store.getters[
      `${GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME}/hasFilterState`
    ](gridId);
  }

  function setFilterState(
    gridId: SocketGroup,
    filterState: ButtonFiltersControllerState
  ): void {
    store.dispatch(
      `${GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME}/setFilterState`,
      { gridId, filterState }
    );
  }

  function clearFilterState(gridId: SocketGroup): void {
    store.dispatch(
      `${GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME}/clearFilterState`,
      gridId
    );
  }

  function clearAllFilterStates(): void {
    store.dispatch(
      `${GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME}/clearAllFilterStates`
    );
  }

  return {
    gridFilterStateStoreState,
    getFilterState,
    hasFilterState,
    setFilterState,
    clearFilterState,
    clearAllFilterStates
  };
}
