import {
  ICleoGoogleMarker,
  ICleoMapControllerState,
  MARKER_TYPE
} from "@/calls/maps/map-models";

import { computed, reactive } from "@vue/composition-api";
import { IBaseSummary } from "@/bases/base-models";
import { BaseData } from "@/bases/base-data";
import { BaseService } from "@/bases/base-service";
import { MapService } from "@/calls/maps/map-service";
import { MapData } from "@/calls/maps/map-data";
import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import VehicleInfoWindowComponent from "@/calls/maps/vehicles/vehicle-info-window.vue";
import CallInfoPopWindowComponent from "@/calls/maps/calls/call-info-pop-window.vue";
import { appStore } from "@/store/store";
import { IUserStoreState, USER_STORE_CONST } from "@/user/user-store";
import { IVehicle } from "@/vehicles/vehicles-models";
import { VehiclesData } from "@/vehicles/vehicles-data";
import DistanceMatrixResponse = google.maps.DistanceMatrixResponse;
import { CommonService, notEmpty } from "@/common/common-service";
import DirectionsResult = google.maps.DirectionsResult;
import GeocoderResponse = google.maps.GeocoderResponse;
import { loggerInstance } from "@/common/Logger";
import QueryAutocompletePrediction = google.maps.places.QueryAutocompletePrediction;
import PlacesServiceStatus = google.maps.places.PlacesServiceStatus;
import AutocompleteService = google.maps.places.AutocompleteService;
import PlacesService = google.maps.places.PlacesService;
import PlaceResult = google.maps.places.PlaceResult;
import PlaceDetailsRequest = google.maps.places.PlaceDetailsRequest;
import Vue from "vue";
import { ICallDetail } from "@/calls/details/call-details-models";

const baseService = new BaseService();
const mapService = new MapService();
const mapData = new MapData();
const vehiclesData = new VehiclesData();
const commonService = new CommonService();

let cleoGoogleMap: google.maps.Map;
let directionsService: google.maps.DirectionsService;
let directionsRenderer: google.maps.DirectionsRenderer;
const directionsRenderers: google.maps.DirectionsRenderer[] = [];
let autoCompleteService: google.maps.places.AutocompleteService;
let placesService: google.maps.places.PlacesService;
let geocoder: google.maps.Geocoder;

import { MarkerClusterer } from "@googlemaps/markerclusterer";

const cleoMapControllerState = reactive<ICleoMapControllerState>({
  currentCall: {
    marker: {}
  },
  bases: {
    isLoading: false,
    objs: [],
    markers: []
  },
  calls: {
    isLoading: false,
    objs: [],
    markers: []
  },
  cars: {
    isLoading: false,
    objs: [],
    markers: [],
    assignedCalls: {},
    infoWindowMap: {}
  }
});

const store = appStore;
const userStoreState = computed<IUserStoreState>(() => {
  return store.state[USER_STORE_CONST.USER__CONST_MODULE_NAME];
});

export interface IMapController {
  setGoogleMap: (googleMap: google.maps.Map) => void;
  getGoogleMap: () => google.maps.Map;
  cleoMapControllerState: ICleoMapControllerState;
  loadBases: () => Promise<void>;
  baseClicked: (base: IBaseSummary) => void;
  loadCalls: () => void;
  loadVehicles: () => void;
  addVehiclesToMap: (vehicles: IVehicle[]) => void;
  addBasesToMap: () => void;
  removeMarkersFromMap: (markers: google.maps.Marker[]) => void;
  bounceMarkers: (markers: google.maps.Marker[], isBouncing: boolean) => void;
  bounceCallMarker(
    cleoCallSummary: ICleoCallSummary
  ): google.maps.Marker | null;
  calculateRouteBetweenTwoMarkers: (
    markerStart: google.maps.Marker,
    markerEnd: google.maps.Marker
  ) => void;
  getCallMarker: (
    cleoCallSummary: ICleoCallSummary
  ) => google.maps.Marker | null;
  getMarker: (
    obj: ICleoCallSummary | IBaseSummary | IVehicle,
    markerType: MARKER_TYPE
  ) => ICleoGoogleMarker | null;
  openMarkerInfoWindow: (marker: ICleoGoogleMarker) => void;
  clearRoutes: () => void;
  getMarkersInBounds: (markerType: MARKER_TYPE) => google.maps.Marker[];
  panToLocation: (
    latLng: google.maps.LatLng | google.maps.LatLngLiteral
  ) => void;
  getAddresses: (
    latLng: google.maps.LatLng | google.maps.LatLngLiteral
  ) => Promise<google.maps.GeocoderResult[]>;
  autoCompleteLookup: (
    query: string
  ) => Promise<{
    predictions: QueryAutocompletePrediction[] | null;
    status: PlacesServiceStatus;
  } | null>;
  getDetails: (
    prediction: QueryAutocompletePrediction
  ) => Promise<PlaceResult | null>;
  // addCurrentCallToMap: (
  //   placeResult: PlaceResult,
  //   callData: ICallDetail | ICleoCallSummary,
  //   vehicles?: IVehicle[]
  // ) => void;
}

function mapControllerFactory(): IMapController {
  function loadBases(): Promise<void> {
    cleoMapControllerState.bases.isLoading = true;
    return new BaseData()
      .getCleoBases()
      .then(resp => {
        const validBases = baseService.getValidBases(Object.values(resp));
        cleoMapControllerState.bases.objs = validBases;
      })
      .finally(() => {
        cleoMapControllerState.bases.isLoading = false;
      });
  }

  function addBasesToMap() {
    const hospitalIcon: string = require("../../assets/hospital.png");
    cleoMapControllerState.bases.objs.forEach(base => {
      const marker = new google.maps.Marker({
        position: {
          lat: base.lat,
          lng: base.long
        },
        title: "Base: " + base.Name,
        icon: hospitalIcon
      });
      (marker as ICleoGoogleMarker).id = mapService.getMarkerId(base, "BASE");

      marker.setMap(cleoGoogleMap);
      cleoMapControllerState.bases.markers.push(marker as ICleoGoogleMarker);

      const contentHtml = mapService.createBasePopOverHtml(base);
      const infoWindow = new google.maps.InfoWindow({
        content: contentHtml
      });

      marker.addListener("click", () => {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        infoWindow.open({ anchor: marker, cleoGoogleMap, shouldFocus: false });
        return;
      });
    });
  }

  function baseClicked(base: IBaseSummary): void {
    const baseMarker = mapService.getBaseMarker(
      base,
      cleoMapControllerState.bases.markers
    );
    if (baseMarker) {
      cleoGoogleMap.panTo(baseMarker.getPosition() as google.maps.LatLng);
    }
  }

  function loadCalls(): void {
    cleoMapControllerState.calls.isLoading = true;
    mapData
      .getCalls()
      .then(resp => {
        setCallsState(resp.Records);
        setVehicleAssignedCalls(resp.Records);
        addCallsToMap(cleoMapControllerState.calls.objs);
      })
      .finally(() => {
        cleoMapControllerState.calls.isLoading = false;
      });
  }

  function setCallsState(cleoCallSummaries: ICleoCallSummary[]): void {
    cleoMapControllerState.calls.objs = cleoCallSummaries;
  }

  function setVehicleAssignedCalls(
    cleoCallSummaries: ICleoCallSummary[]
  ): void {
    cleoMapControllerState.cars.assignedCalls = commonService.convertArrayToObjectArray(
      "DispatchVehicle",
      cleoCallSummaries.filter(cleoCallSummary => {
        return cleoCallSummary.DispatchVehicle.length > 0;
      })
    );
  }

  function addCallsToMap(cleoCallSummaries: ICleoCallSummary[]): void {
    cleoCallSummaries.forEach(cleoCallSummary => {
      addCallToMap(cleoCallSummary);
    });
  }

  function addCallToMap(cleoCallSummary: ICleoCallSummary): void {
    const redDrop: string = require("../../assets/reddrop.png");
    const greenDrop: string = require("../../assets/greendrop.png");
    const redPin: string = require("../../assets/red-pushpin.png");
    const bluePin: string = require("../../assets/blue-pushpin.png");
    //
    // if (cleoCallSummary.Long && cleoCallSummary.Lat) {
    //   const marker = new google.maps.Marker({
    //     position: {
    //       lat: cleoCallSummary.Lat,
    //       lng: cleoCallSummary.Long
    //     },
    //     title:
    //       "Call: " +
    //       cleoCallSummary.CallNo +
    //       " - " +
    //       cleoCallSummary.CallForename +
    //       " " +
    //       cleoCallSummary.CallSurname,
    //     icon:
    //       cleoCallSummary.DispatchVehicle.length > 0
    //         ? cleoCallSummary.CallUrgentYn
    //           ? redPin
    //           : bluePin
    //         : cleoCallSummary.CallUrgentYn
    //         ? redDrop
    //         : greenDrop,
    //     id: mapService.getMarkerId(cleoCallSummary, "CALL")
    //   });
    //
    //   marker.setMap(cleoGoogleMap);
    //   cleoMapControllerState.calls.markers.push(marker);
    //
    //   const CallInfoWindow = Vue.extend(CallInfoWindowComponent);
    //   const instance = new CallInfoWindow({
    //     propsData: {
    //       cleoCallSummary: cleoCallSummary
    //     }
    //   });
    //   instance.$mount();
    //
    //   instance.$on("openCall", function(cleoCallSummary: ICleoCallSummary) {
    //     callSummaryController.openCall(
    //       cleoCallSummary,
    //       userStoreState.value.user
    //     );
    //   });
    //
    //   instance.$on("findNearestVehicles", function(payload: {
    //     cleoCallSummary: ICleoCallSummary;
    //     nearestCount: number;
    //   }) {
    //     const marker = mapService.getCallMarker(
    //       payload.cleoCallSummary,
    //       cleoMapControllerState.calls.markers
    //     );
    //     if (marker) {
    //       let sourceMarkerPosition = payload.cleoCallSummary.CallPostCode;
    //       if (payload.cleoCallSummary.Lat && payload.cleoCallSummary.Long) {
    //         sourceMarkerPosition = new google.maps.LatLng(
    //           payload.cleoCallSummary.Lat,
    //           payload.cleoCallSummary.Long
    //         );
    //       }
    //
    //       getNearestMarkersV2(
    //         sourceMarkerPosition,
    //         cleoMapControllerState.cars.markers,
    //         payload.nearestCount
    //       );
    //     }
    //   });
    //
    //   const infoWindow = new google.maps.InfoWindow({
    //     content: instance.$el
    //   });
    //
    //   marker.addListener("click", () => {
    //     infoWindow.open({
    //       anchor: marker,
    //       cleoGoogleMap,
    //       shouldFocus: false
    //     });
    //   });
    // }
  }

  function loadVehicles() {
    cleoMapControllerState.cars.isLoading = true;
    vehiclesData
      .getVehicles()
      .then(resp => {
        cleoMapControllerState.cars.objs = resp.Records;
        addVehiclesToMap(cleoMapControllerState.cars.objs);
      })
      .finally(() => {
        cleoMapControllerState.cars.isLoading = false;
      });
  }

  function addVehiclesToMap(vehicles: IVehicle[]): void {
    vehicles.forEach(vehicle => {
      const carImgId =
        (vehicle.id.toString().length === 1 ? "0" : "") + vehicle.id;
      const vehichleIcon: string = require("../../assets/car-01.png");

      const markerId = mapService.getMarkerId(vehicle, "CAR");
      const marker = new google.maps.Marker({
        position: {
          lat: vehicle.Lat,
          lng: vehicle.Long
        },
        title: "Car: " + vehicle.id + " - " + vehicle.name,
        icon: vehichleIcon
      });

      (marker as ICleoGoogleMarker).id = markerId;
      //  See MarkerClusterer below
      // marker.setMap(cleoGoogleMap);
      cleoMapControllerState.cars.markers.push(marker as ICleoGoogleMarker);

      const cleoCallSummariesForVehicle = mapService.getCallsForVehicle(
        cleoMapControllerState.calls.objs,
        vehicle
      );

      const VehicleInfoWindow = Vue.extend(VehicleInfoWindowComponent);
      const instance = new VehicleInfoWindow({
        propsData: {
          vehicle,
          cleoCallSummaries: cleoCallSummariesForVehicle
        }
      });
      instance.$mount();

      instance.$on("showAssignedCalls", function(vehicle: IVehicle) {
        loggerInstance.log(
          "showAssignedCalls for: " + vehicle.id + "-" + vehicle.name
        );

        const matchingMarkers = getCallMarkersForVehicle(vehicle);
        bounceMarkers(matchingMarkers, true);
      });

      const infoWindow = new google.maps.InfoWindow({
        content: instance.$el
      });
      cleoMapControllerState.cars.infoWindowMap[markerId] = infoWindow;

      marker.addListener("click", () => {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        infoWindow.open({ anchor: marker, cleoGoogleMap, shouldFocus: false });
        return;
      });

      // marker.addListener("click", () => {
      //   infoWindow.open({
      //     anchor: marker,
      //     cleoGoogleMap,
      //     shouldFocus: false
      //   });
      // });
    });

    // https://github.com/googlemaps/js-markerclusterer
    const myMap: google.maps.Map = cleoGoogleMap as google.maps.Map;
    const myMarkers: google.maps.Marker[] = cleoMapControllerState.cars
      .markers as google.maps.Marker[];
    new MarkerClusterer({ markers: myMarkers, map: myMap });
  }

  function getCallMarkersForVehicle(vehicle: IVehicle): google.maps.Marker[] {
    const callsForVehicle = mapService.getCallsForVehicle(
      cleoMapControllerState.calls.objs,
      vehicle
    );
    const markerIds = callsForVehicle.map(cleoCallSummary => {
      return mapService.getMarkerId(cleoCallSummary, "CALL");
    });
    return cleoMapControllerState.calls.markers.filter(marker => {
      return markerIds.indexOf(marker.id) > -1;
    });
  }

  function getMarkersInBounds(markerType: MARKER_TYPE): google.maps.Marker[] {
    let markers: google.maps.Marker[] = [];
    if (markerType === "CAR") {
      markers = cleoMapControllerState.cars.markers;
    }
    if (markerType === "CALL") {
      markers = cleoMapControllerState.calls.markers;
    }
    if (markerType === "BASE") {
      markers = cleoMapControllerState.bases.markers;
    }
    return markers.filter(marker => {
      return cleoGoogleMap
        .getBounds()
        ?.contains(marker.getPosition() as google.maps.LatLng);
    });
  }

  function getNearestMarkersV2(
    sourcePosition: string | google.maps.LatLng,
    destinationMarkers: google.maps.Marker[],
    nearestCount: number
  ): void {
    //  TODO if destinationMarkers less than "5" ??? then don;t do line of sight.
    //  Because we can't hit Google with 40 car geolocations every time,
    //  we need to do a bit of pre-filtering.
    // const nearestByLineOfSight: {
    //   distance: number;
    //   marker: google.maps.Marker;
    // }[] = destinationMarkers
    //   .reduce((accum, marker: google.maps.Marker) => {
    //     const distance = google.maps.geometry.spherical.computeDistanceBetween(
    //       sourcePosition,
    //       marker.getPosition()
    //     );
    //     accum.push({
    //       distance,
    //       marker
    //     });
    //     return accum;
    //   }, [] as { distance: number; marker: google.maps.Marker }[])
    //   .sort((a, b) => {
    //     return a.distance - b.distance;
    //   });
    //
    // //  TODO geolocate nearest.
    // const MAX_ALLOWED_TO_GEOLOCATE = 5;
    // const destMarkers: google.maps.Marker[] = destinationMarkers
    //   .slice(
    //     0,
    //     nearestCount > MAX_ALLOWED_TO_GEOLOCATE
    //       ? MAX_ALLOWED_TO_GEOLOCATE
    //       : nearestCount
    //   )
    //   .filter(notEmpty);
    //
    // const nearestMarkers = nearestByLineOfSight.map(markerDist => {
    //   return markerDist.marker;
    // });
    //
    // const distanceMatrixService = new google.maps.DistanceMatrixService();
    // const geocoder = new google.maps.Geocoder();
    //
    // const destPositions: google.maps.LatLng[] = nearestMarkers
    //   .slice(0, nearestCount)
    //   .map(marker => {
    //     return marker.getPosition();
    //   })
    //   .filter(notEmpty);
    //
    // const request: google.maps.DistanceMatrixRequest = {
    //   origins: [sourcePosition],
    //   destinations: destPositions,
    //   travelMode: google.maps.TravelMode.DRIVING,
    //   unitSystem: google.maps.UnitSystem.METRIC
    // };
    // distanceMatrixService
    //   .getDistanceMatrix(request)
    //   .then((response: DistanceMatrixResponse) => {
    //     const originAddresses = response.originAddresses;
    //     //  we are only allowing ONE source.
    //     const originSourceAddress = originAddresses[0];
    //     const destinationList = response.destinationAddresses;
    //
    //     const geocodeProms = [];
    //     const geocodeResultsOrigin: GeocoderResponse[] = [];
    //     const geocodeResultsDest: GeocoderResponse[] = [];
    //
    //     const promOrigin = geocoder
    //       .geocode({ address: originSourceAddress })
    //       .then((resp: GeocoderResponse) => {
    //         geocodeResultsOrigin.push(resp);
    //       });
    //     geocodeProms.push(promOrigin);
    //
    //     for (let j = 0; j < destinationList.length; j++) {
    //       const promDest = geocoder
    //         .geocode({ address: destinationList[j] })
    //         .then((resp: GeocoderResponse) => {
    //           geocodeResultsDest.push(resp);
    //         });
    //       geocodeProms.push(promDest);
    //     }
    //
    //     Promise.all(geocodeProms).then(() => {
    //       const origin = geocodeResultsOrigin[0].results[0];
    //       const originLocation = origin.geometry.location;
    //       const furthestLocation =
    //         geocodeResultsDest[0].results[0].geometry.location;
    //
    //       const bounds = new google.maps.LatLngBounds();
    //       bounds.extend(originLocation);
    //       bounds.extend(furthestLocation);
    //       cleoGoogleMap.fitBounds(bounds);
    //
    //       for (let j = 0; j < geocodeResultsDest.length; j++) {
    //         const dest = geocodeResultsDest[j].results[0];
    //         const destLocation = dest.geometry.location;
    //         calculateRoute(originLocation, destLocation);
    //       }
    //     });
    //   });
  }

  function getNearestMarkers(
    sourcePosition: string | google.maps.LatLng,
    destinationMarkers: google.maps.Marker[],
    nearestCount: number
  ) {
    const distanceMatrixService = new google.maps.DistanceMatrixService();
    const geocoder = new google.maps.Geocoder();

    const MAX_NEAREST_COUNT = 4;

    nearestCount =
      nearestCount > MAX_NEAREST_COUNT ? MAX_NEAREST_COUNT : nearestCount;

    const destPositions: google.maps.LatLng[] = destinationMarkers
      .slice(0, nearestCount)
      .map(marker => {
        return marker.getPosition();
      })
      .filter(notEmpty);

    const request: google.maps.DistanceMatrixRequest = {
      origins: [sourcePosition],
      destinations: destPositions,
      travelMode: google.maps.TravelMode.DRIVING,
      unitSystem: google.maps.UnitSystem.METRIC
    };
    distanceMatrixService
      .getDistanceMatrix(request)
      .then((response: DistanceMatrixResponse) => {
        const originAddresses = response.originAddresses;
        //  we are only allowing ONE source.
        const originSourceAddress = originAddresses[0];
        const destinationList = response.destinationAddresses;

        const geocodeProms = [];
        const geocodeResultsOrigin: GeocoderResponse[] = [];
        const geocodeResultsDest: GeocoderResponse[] = [];

        const promOrigin = geocoder
          .geocode({ address: originSourceAddress })
          .then((resp: GeocoderResponse) => {
            geocodeResultsOrigin.push(resp);
          });
        geocodeProms.push(promOrigin);

        for (let j = 0; j < destinationList.length; j++) {
          const promDest = geocoder
            .geocode({ address: destinationList[j] })
            .then((resp: GeocoderResponse) => {
              geocodeResultsDest.push(resp);
            });
          geocodeProms.push(promDest);
        }

        Promise.all(geocodeProms).then(() => {
          const origin = geocodeResultsOrigin[0].results[0];
          const originLocation = origin.geometry.location;
          const furthestLocation =
            geocodeResultsDest[0].results[0].geometry.location;

          const bounds = new google.maps.LatLngBounds();
          bounds.extend(originLocation);
          bounds.extend(furthestLocation);
          cleoGoogleMap.fitBounds(bounds);

          for (let j = 0; j < geocodeResultsDest.length; j++) {
            const dest = geocodeResultsDest[j].results[0];
            const destLocation = dest.geometry.location;
            calculateRoute(originLocation, destLocation);
          }
        });
      });
  }

  function calculateRoute(
    start: string | google.maps.LatLng,
    end: string | google.maps.LatLng
  ): void {
    // const directionsService: google.maps.DirectionsService = new google.maps.DirectionsService();
    const directionsRenderer = new google.maps.DirectionsRenderer();
    directionsRenderer.setMap(cleoGoogleMap);
    // getDirectionsRendererInstance().setMap(cleoGoogleMap);

    directionsRenderers.push(directionsRenderer);

    const request: google.maps.DirectionsRequest = {
      origin: start,
      destination: end,
      travelMode: google.maps.TravelMode.DRIVING
    };
    // directionsService.route(request).then((result: DirectionsResult) => {
    //   directionsRenderer.setDirections(result);
    // });
    getDirectionsServiceInstance()
      .route(request)
      .then((result: DirectionsResult) => {
        directionsRenderer.setDirections(result);
      })
      .catch(err => {
        //  TODO  get generic ErrorLogger
      });
  }

  function calculateRouteBetweenTwoMarkers(
    markerStart: google.maps.Marker,
    markerEnd: google.maps.Marker
  ) {
    const distanceMatrixService = new google.maps.DistanceMatrixService();
    const geocoder = new google.maps.Geocoder();

    const originLatLng: google.maps.LatLng = markerStart.getPosition() as google.maps.LatLng;
    const destLatLng: google.maps.LatLng = markerEnd.getPosition() as google.maps.LatLng;

    const request: google.maps.DistanceMatrixRequest = {
      origins: [originLatLng],
      destinations: [destLatLng],
      travelMode: google.maps.TravelMode.DRIVING,
      unitSystem: google.maps.UnitSystem.METRIC
    };
    distanceMatrixService
      .getDistanceMatrix(request)
      .then((response: DistanceMatrixResponse) => {
        const originAddresses = response.originAddresses;
        const originSourceAddress = originAddresses[0];
        const destinationList = response.destinationAddresses;

        const geocodeProms = [];
        const geocodeResultsOrigin: GeocoderResponse[] = [];
        const geocodeResultsDest: GeocoderResponse[] = [];

        const promOrigin = geocoder
          .geocode({ address: originSourceAddress })
          .then((resp: GeocoderResponse) => {
            geocodeResultsOrigin.push(resp);
          });
        geocodeProms.push(promOrigin);

        for (let j = 0; j < destinationList.length; j++) {
          const promDest = geocoder
            .geocode({ address: destinationList[j] })
            .then((resp: GeocoderResponse) => {
              geocodeResultsDest.push(resp);
            });
          geocodeProms.push(promDest);
        }

        Promise.all(geocodeProms).then(() => {
          const origin = geocodeResultsOrigin[0].results[0];
          const originLocation = origin.geometry.location;
          const furthestLocation =
            geocodeResultsDest[0].results[0].geometry.location;

          const bounds = new google.maps.LatLngBounds();
          bounds.extend(originLocation);
          bounds.extend(furthestLocation);
          cleoGoogleMap.fitBounds(bounds);

          for (let j = 0; j < geocodeResultsDest.length; j++) {
            const dest = geocodeResultsDest[j].results[0];
            const destLocation = dest.geometry.location;
            calculateRoute(originLocation, destLocation);
          }
        });
      });
  }

  function removeMarkersFromMap(markers: google.maps.Marker[]) {
    for (let i = 0; i < markers.length; i++) {
      markers[i].setMap(null);
    }
  }

  function bounceMarkers(markers: google.maps.Marker[], isBouncing: boolean) {
    for (let i = 0; i < markers.length; i++) {
      const marker = markers[i];
      if (isBouncing) {
        marker.setAnimation(google.maps.Animation.BOUNCE);
      } else {
        marker.setAnimation(null);
      }
    }

    if (markers.length === 1) {
      cleoGoogleMap.panTo(markers[0].getPosition() as google.maps.LatLng);
    }
  }

  function getMarker(
    obj: ICleoCallSummary | IBaseSummary | IVehicle,
    markerType: MARKER_TYPE
  ): ICleoGoogleMarker | null {
    let markers: ICleoGoogleMarker[] = [];
    if (markerType === "CALL") {
      markers = cleoMapControllerState.calls.markers;
    }
    if (markerType === "CAR") {
      markers = cleoMapControllerState.cars.markers;
    }
    if (markerType === "BASE") {
      markers = cleoMapControllerState.bases.markers;
    }
    const markerId = mapService.getMarkerId(obj, markerType);
    return mapService.getMarker(markerId, markers);
  }

  function openMarkerInfoWindow(marker: ICleoGoogleMarker): void {
    google.maps.event.trigger(marker, "click");
  }

  function getCallMarker(
    cleoCallSummary: ICleoCallSummary
  ): google.maps.Marker | null {
    return mapService.getCallMarker(
      cleoCallSummary,
      cleoMapControllerState.calls.markers
    );
  }

  function bounceCallMarker(
    cleoCallSummary: ICleoCallSummary
  ): google.maps.Marker | null {
    const callMarker = getCallMarker(cleoCallSummary);
    if (callMarker) {
      bounceMarkers([callMarker], true);
      return callMarker;
    }
    return null;
  }

  function clearRoutes() {
    // getDirectionsRendererInstance().setMap(null);
    directionsRenderers.forEach(directionsRenderer => {
      directionsRenderer.setMap(null);
    });
  }

  function getAutocompleteService(): AutocompleteService {
    if (!autoCompleteService) {
      autoCompleteService = new google.maps.places.AutocompleteService();
    }
    return autoCompleteService;
  }

  function autoCompleteLookup(
    query: string
  ): Promise<{
    predictions: QueryAutocompletePrediction[] | null;
    status: PlacesServiceStatus;
  } | null> {
    if (query.length === 0) {
      return Promise.resolve(null);
    }
    // getPlacePredictions
    return new Promise(resolve => {
      getAutocompleteService().getQueryPredictions(
        {
          input: query
        },
        (
          predictions: QueryAutocompletePrediction[] | null,
          status: PlacesServiceStatus
        ) => {
          // loggerInstance.log("getQueryPredictions()", {predictions, status});
          resolve({ predictions, status });
          return { predictions, status };
        }
      );
    });
  }

  function getPlacesService(): PlacesService {
    if (!placesService) {
      placesService = new google.maps.places.PlacesService(cleoGoogleMap);
    }
    return placesService;
  }

  function getDetails(
    prediction: QueryAutocompletePrediction
  ): Promise<PlaceResult | null> {
    if (prediction && prediction.place_id) {
      const placeDetailsRequest: PlaceDetailsRequest = {
        placeId: prediction.place_id
      };

      return new Promise(resolve => {
        getPlacesService().getDetails(
          placeDetailsRequest,
          (placeResult: PlaceResult | null) => {
            return resolve(placeResult);
          }
        );
      });
    }
    return Promise.resolve(null);
  }

  function panToLocation(
    latLng: google.maps.LatLng | google.maps.LatLngLiteral
  ): void {
    cleoGoogleMap.panTo(latLng);
  }

  function getAddresses(
    latLng: google.maps.LatLng | google.maps.LatLngLiteral
  ): Promise<google.maps.GeocoderResult[]> {
    if (!geocoder) {
      geocoder = new google.maps.Geocoder();
    }

    return geocoder
      .geocode({
        location: latLng
      })
      .then((resp: google.maps.GeocoderResponse) => {
        return resp.results;
      });
  }

  /*
  function addCurrentCallToMap(
    placeResult: PlaceResult,
    callData: ICallDetail | ICleoCallSummary,
    vehicles: IVehicle[] = []
  ) {
    const icon: string = require("../../assets/red-down-arrow.png");

    const title = "Current Call: " + callData.CallNo;
    // const html = mapService.getCallPopOverHtml(callData);

    const marker = new google.maps.Marker({
      position: placeResult.geometry?.location as google.maps.LatLng,
      title: title,
      icon: icon
    });
    (marker as ICleoGoogleMarker).id = callData.CallNo.toString();
    marker.setMap(cleoGoogleMap);

    cleoGoogleMap.setMapTypeId("satellite");

    marker.addListener("click", () => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      infoWindow.open({ anchor: marker, cleoGoogleMap, shouldFocus: false });
      return;
    });

    //  This is the component to display in the pop up
    const CallInfoPopWindow = Vue.extend(CallInfoPopWindowComponent);
    const instance = new CallInfoPopWindow({
      propsData: {
        cleoCall: callData,
        vehicles: vehicles
      }
    });
    instance.$mount();

    instance.$on("dispatchCall", function(
      callData: ICallDetail | ICleoCallSummary
    ) {
      // callSummaryController.openCall(
      //   cleoCallSummary,
      //   userStoreState.value.user
      // );
    });

    const infoWindow = new google.maps.InfoWindow({
      content: instance.$el
    });

    //  ...default display the info.
    infoWindow.open(cleoGoogleMap, marker);
  }
  */

  return {
    setGoogleMap: (googleMap: google.maps.Map) => {
      cleoGoogleMap = googleMap;
    },
    getGoogleMap: () => {
      return cleoGoogleMap;
    },
    cleoMapControllerState,
    loadBases,
    baseClicked,
    loadCalls,
    loadVehicles,
    addVehiclesToMap,
    addBasesToMap,
    removeMarkersFromMap,
    bounceMarkers,
    bounceCallMarker,
    calculateRouteBetweenTwoMarkers,
    getCallMarker,
    getMarker,
    openMarkerInfoWindow,
    clearRoutes,
    getMarkersInBounds,
    panToLocation,
    getAddresses,
    autoCompleteLookup,
    getDetails
  };
}

// export function useMapController
let mapControllerInstance: IMapController;

export function getMapControllerInstance(): IMapController {
  if (!mapControllerInstance) {
    mapControllerInstance = mapControllerFactory();
  }
  return mapControllerInstance;
}

// let directionsService: google.maps.DirectionsService;
// let directionsRenderer: google.maps.DirectionsRenderer;

export function getDirectionsServiceInstance(): google.maps.DirectionsService {
  if (!directionsService) {
    directionsService = new google.maps.DirectionsService();
  }
  return directionsService;
}

export function getDirectionsRendererInstance(): google.maps.DirectionsRenderer {
  if (!directionsRenderer) {
    directionsRenderer = new google.maps.DirectionsRenderer();
  }
  return directionsRenderer;
}
