# Grid Filter Persistence Implementation Plan

## Overview
This document provides a complete implementation plan for adding persistent filter states across grid navigation in the CLEO frontend application.

## 1. Create Grid Filter State Store Module

### File: `src/calls/grids/grid-filter/grid-filter-state-store.ts`

```typescript
import { Module } from "vuex";
import { IRootState } from "@/store/store";
import { SocketGroup } from "@/calls/grids/grid-models";
import { ButtonFiltersControllerState } from "@/calls/grids/grid-filter/generic-filters/models/useButtonFiltersController";
import { factoryButtonFiltersControllerState } from "@/calls/grids/grid-filter/generic-filters/models/generic-filters-service";
import Vue from "vue";

export interface IGridFilterStateStoreState {
  filterStates: Record<SocketGroup, ButtonFiltersControllerState>;
}

export enum GRID_FILTER_STATE_STORE_CONST {
  GRID_FILTER_STATE_STORE__CONST_MODULE_NAME = "GRID_FILTER_STATE_STORE__CONST_MODULE_NAME",
  
  GRID_FILTER_STATE_STORE__MUTATION_SET_FILTER_STATE = "GRID_FILTER_STATE_STORE__MUTATION_SET_FILTER_STATE",
  GRID_FILTER_STATE_STORE__MUTATION_CLEAR_FILTER_STATE = "GRID_FILTER_STATE_STORE__MUTATION_CLEAR_FILTER_STATE",
  GRID_FILTER_STATE_STORE__MUTATION_CLEAR_ALL_FILTER_STATES = "GRID_FILTER_STATE_STORE__MUTATION_CLEAR_ALL_FILTER_STATES"
}

const state: IGridFilterStateStoreState = {
  filterStates: {}
};

const mutations = {
  [GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__MUTATION_SET_FILTER_STATE](
    state: IGridFilterStateStoreState,
    payload: { gridId: SocketGroup; filterState: ButtonFiltersControllerState }
  ): void {
    Vue.set(state.filterStates, payload.gridId, payload.filterState);
  },

  [GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__MUTATION_CLEAR_FILTER_STATE](
    state: IGridFilterStateStoreState,
    gridId: SocketGroup
  ): void {
    Vue.delete(state.filterStates, gridId);
  },

  [GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__MUTATION_CLEAR_ALL_FILTER_STATES](
    state: IGridFilterStateStoreState
  ): void {
    state.filterStates = {};
  }
};

const getters = {
  getFilterState: (state: IGridFilterStateStoreState) => (gridId: SocketGroup): ButtonFiltersControllerState | null => {
    return state.filterStates[gridId] || null;
  },
  
  hasFilterState: (state: IGridFilterStateStoreState) => (gridId: SocketGroup): boolean => {
    return !!state.filterStates[gridId];
  },
  
  getAllFilterStates: (state: IGridFilterStateStoreState): Record<SocketGroup, ButtonFiltersControllerState> => {
    return state.filterStates;
  }
};

const actions = {
  setFilterState({ commit }: any, payload: { gridId: SocketGroup; filterState: ButtonFiltersControllerState }) {
    commit(GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__MUTATION_SET_FILTER_STATE, payload);
  },
  
  clearFilterState({ commit }: any, gridId: SocketGroup) {
    commit(GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__MUTATION_CLEAR_FILTER_STATE, gridId);
  },
  
  clearAllFilterStates({ commit }: any) {
    commit(GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__MUTATION_CLEAR_ALL_FILTER_STATES);
  }
};

export const gridFilterStateStore: Module<IGridFilterStateStoreState, IRootState> = {
  namespaced: true,
  state,
  mutations,
  getters,
  actions
};
```

## 2. Update Root Store

### File: `src/store/store.ts`

Add the new store module to the root store:

```typescript
// Add import
import {
  IGridFilterStateStoreState,
  GRID_FILTER_STATE_STORE_CONST,
  gridFilterStateStore
} from "@/calls/grids/grid-filter/grid-filter-state-store";

// Update IRootState interface
export interface IRootState {
  [SOCKET_STORE_CONST.SOCKET__CONST_MODULE_NAME]: ISocketStoreState;
  [CONFIG_STORE_CONST.CONFIG__CONST_MODULE_NAME]: IConfigStoreState;
  [PERMISSION_STORE_CONST.PERMISSION__CONST_MODULE_NAME]: IPermissionStoreState;
  [USER_STORE_CONST.USER__CONST_MODULE_NAME]: IUserStoreState;
  [CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__CONST_MODULE_NAME]: IContextMenuStoreState;
  [LOGIN_STORE_STORE_CONST.LOGIN_STORE__CONST_MODULE_NAME]: ILoginStoreState;
  [KEYWORD_STORE_STORE_CONST.KEYWORD_STORE__CONST_MODULE_NAME]: IKeywordsStoreState;
  [PACCS_STORE_CONST.PACCS__CONST_MODULE_NAME]: IPaccsStoreState;
  [GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME]: IGridFilterStateStoreState;
}

// Update store modules
const store: StoreOptions<IRootState> = {
  strict: true,
  modules: {
    [SOCKET_STORE_CONST.SOCKET__CONST_MODULE_NAME]: socketStore,
    [CONFIG_STORE_CONST.CONFIG__CONST_MODULE_NAME]: configStore,
    [PERMISSION_STORE_CONST.PERMISSION__CONST_MODULE_NAME]: permissionStore,
    [USER_STORE_CONST.USER__CONST_MODULE_NAME]: userStore,
    [CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__CONST_MODULE_NAME]: contextMenuStore,
    [LOGIN_STORE_STORE_CONST.LOGIN_STORE__CONST_MODULE_NAME]: loginStore,
    [KEYWORD_STORE_STORE_CONST.KEYWORD_STORE__CONST_MODULE_NAME]: keywordStore,
    [PACCS_STORE_CONST.PACCS__CONST_MODULE_NAME]: paccsStore,
    [GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME]: gridFilterStateStore
  }
};
```

## 3. Create Composable Hook for Filter State Management

### File: `src/calls/grids/grid-filter/useGridFilterState.ts`

```typescript
import { computed } from "@vue/composition-api";
import { useStore } from "@/store/store";
import { SocketGroup } from "@/calls/grids/grid-models";
import { ButtonFiltersControllerState } from "@/calls/grids/grid-filter/generic-filters/models/useButtonFiltersController";
import {
  GRID_FILTER_STATE_STORE_CONST,
  IGridFilterStateStoreState
} from "@/calls/grids/grid-filter/grid-filter-state-store";

export function useGridFilterState() {
  const store = useStore();

  const gridFilterStateStoreState = computed<IGridFilterStateStoreState>(() => {
    return store.state[GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME];
  });

  function getFilterState(gridId: SocketGroup): ButtonFiltersControllerState | null {
    return store.getters[
      `${GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME}/getFilterState`
    ](gridId);
  }

  function hasFilterState(gridId: SocketGroup): boolean {
    return store.getters[
      `${GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME}/hasFilterState`
    ](gridId);
  }

  function setFilterState(gridId: SocketGroup, filterState: ButtonFiltersControllerState): void {
    store.dispatch(
      `${GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME}/setFilterState`,
      { gridId, filterState }
    );
  }

  function clearFilterState(gridId: SocketGroup): void {
    store.dispatch(
      `${GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME}/clearFilterState`,
      gridId
    );
  }

  function clearAllFilterStates(): void {
    store.dispatch(
      `${GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME}/clearAllFilterStates`
    );
  }

  return {
    gridFilterStateStoreState,
    getFilterState,
    hasFilterState,
    setFilterState,
    clearFilterState,
    clearAllFilterStates
  };
}
```

## 4. Update useButtonFiltersController

### File: `src/calls/grids/grid-filter/generic-filters/models/useButtonFiltersController.ts`

Modify the existing controller to support persistence:

```typescript
// Add imports
import { SocketGroup } from "@/calls/grids/grid-models";
import { useGridFilterState } from "@/calls/grids/grid-filter/useGridFilterState";

// Update interface
export interface ButtonFiltersControllerInput {
  gridFilterUserInput: IGridFilterUserInput;
  gridId?: SocketGroup; // Add optional grid identifier
}

// Update the main function
export function useButtonFiltersController(gridId?: SocketGroup) {
  const userStore = useUserStore();
  const gridFilterState = useGridFilterState();

  const state = reactive<ButtonFiltersControllerState>(
    factoryButtonFiltersControllerState()
  );

  function init(input: ButtonFiltersControllerInput) {
    // Try to restore saved state first if gridId is provided
    if (gridId) {
      const savedState = gridFilterState.getFilterState(gridId);
      if (savedState) {
        // Restore the saved state
        Object.assign(state, savedState);
        // Ensure username is current
        state.gridFilterUserInput.MY_CASE_AND_PARAMEDIC_ON_SCENE.userName = formatUserDominoName(
          userStore.user.userName
        );
        return;
      }
    }

    // Fallback to provided input or default state
    state.gridFilterUserInput = simpleObjectClone(input.gridFilterUserInput);
    setGroupedDxCodes();

    state.gridFilterUserInput.MY_CASE_AND_PARAMEDIC_ON_SCENE.userName = formatUserDominoName(
      userStore.user.userName
    );
  }

  function saveCurrentState() {
    if (gridId) {
      gridFilterState.setFilterState(gridId, state);
    }
  }

  function reset() {
    state.gridFilterUserInput = gridFilterService.factoryGridFilterUserInput();
    // Clear saved state when resetting
    if (gridId) {
      gridFilterState.clearFilterState(gridId);
    }
  }

  // Update all toggle/set functions to save state after changes
  function toggleCleoClientService(cleoClientService: CLEO_CLIENT_SERVICE) {
    const cleoClientServices = state.gridFilterUserInput.CLEO_CLIENT_SERVICE;

    const cleoClientServiceInternal = cleoClientService.toUpperCase();

    const isAlreadyInArray = cleoClientServices.includes(
      cleoClientServiceInternal
    );

    if (isAlreadyInArray) {
      state.gridFilterUserInput.CLEO_CLIENT_SERVICE = cleoClientServices.filter(
        (item: CLEO_CLIENT_SERVICE) => {
          // if Toxic Ingestion is selected also remove "" from the array
          if (cleoClientService === "TOXIC INGESTION" && item === "") {
            return false;
          }

          return item !== cleoClientServiceInternal;
        }
      );
    } else {
      state.gridFilterUserInput.CLEO_CLIENT_SERVICE.push(
        cleoClientServiceInternal
      );
      // If Toxic Ingestion is selected also add "" to the array
      if (cleoClientServiceInternal === "TOXIC INGESTION") {
        state.gridFilterUserInput.CLEO_CLIENT_SERVICE.push("");
      }
    }
    
    saveCurrentState(); // Save after change
  }

  function toggleTracedAndVerified(show: boolean) {
    if (state.gridFilterUserInput.PDS_TRACED_AND_VERIFIED === show) {
      state.gridFilterUserInput.PDS_TRACED_AND_VERIFIED = null;
    } else {
      state.gridFilterUserInput.PDS_TRACED_AND_VERIFIED = show;
    }
    saveCurrentState(); // Save after change
  }

  function setAssignedDoctor(isAssignedDoctor: boolean) {
    if (state.gridFilterUserInput.ASSIGNED_TO === isAssignedDoctor) {
      state.gridFilterUserInput.ASSIGNED_TO = null;
    } else {
      state.gridFilterUserInput.ASSIGNED_TO = isAssignedDoctor;
    }
    saveCurrentState(); // Save after change
  }

  function toggleBreached(isBreached: boolean) {
    if (state.gridFilterUserInput.BREACHED === isBreached) {
      state.gridFilterUserInput.BREACHED = null;
    } else {
      state.gridFilterUserInput.BREACHED = isBreached;
    }
    saveCurrentState(); // Save after change
  }

  function toggleMyCasesParamedicOnScene() {
    if (state.gridFilterUserInput.MY_CASE_AND_PARAMEDIC_ON_SCENE.enabled) {
      state.gridFilterUserInput.MY_CASE_AND_PARAMEDIC_ON_SCENE.enabled = null;
    } else {
      state.gridFilterUserInput.MY_CASE_AND_PARAMEDIC_ON_SCENE.enabled = true;
    }
    saveCurrentState(); // Save after change
  }

  function toggleRequiresValidation() {
    if (state.gridFilterUserInput.REQUIRES_VALIDATION) {
      state.gridFilterUserInput.REQUIRES_VALIDATION = null;
    } else {
      state.gridFilterUserInput.REQUIRES_VALIDATION = true;
    }
    saveCurrentState(); // Save after change
  }

  // Apply saveCurrentState() to all other toggle functions...
  // (toggleDxCodeTypes, toggleDxCodes, toggleToxicIngestionAndEmpty, etc.)

  return {
    state,

    init,
    reset,
    saveCurrentState, // Expose for manual saving if needed
    toggleCleoClientService,
    toggleDxCodeTypes,
    toggleDxCodes,
    toggleToxicIngestionAndEmpty,
    toggleTracedAndVerified,
    setAssignedDoctor,
    toggleBreached,
    toggleAmbulance,
    toggleEDValidation,
    toggleMyCasesParamedicOnScene,
    toggleRequiresValidation,
    toggleFollowUp
  };
}
```

## 5. Update ButtonFilters.vue

### File: `src/calls/grids/grid-filter/generic-filters/ui/ButtonFilters.vue`

Update the component to accept and use gridId:

```typescript
// In the props section, add:
props: {
  gridFilterUserInput: {
    type: Object as PropType<IGridFilterUserInput>,
    required: true
  },
  applyFiltersImmediately: {
    type: Boolean,
    default: false
  },
  gridId: {
    type: String as PropType<SocketGroup>,
    required: false
  }
},

// In the setup function:
setup(
  props: {
    gridFilterUserInput: IGridFilterUserInput;
    applyFiltersImmediately: boolean;
    gridId?: SocketGroup;
  },
  context: SetupContext
) {
  const gridFilterService = new GridFilterService();

  const buttonFiltersController = useButtonFiltersController(props.gridId);

  // ... rest of setup logic remains the same
}
```

## 6. Update GridStandard2.vue

### File: `src/calls/grids/grids-named/GridStandard2.vue`

Update the grid component to pass the grid identifier:

```vue
<!-- In the template, update the ButtonFilters component -->
<ButtonFilters
  slot="content"
  style="padding: var(--ic24-flex-gap)"
  :grid-filter-user-input="gridController.state.filterUserInput"
  :apply-filters-immediately="false"
  :grid-id="gridDefinition.identifier"
  @input="filterDialogClosed"
  @cancel="gridController.state.showFilterDialog = false"
/>
```

## 7. Integration Testing Scenarios

### Test Cases to Verify Implementation:

1. **Basic Filter Persistence**
   - Apply filters to CasCalls grid
   - Navigate to PLS grid
   - Return to CasCalls grid
   - Verify filters are restored

2. **Multiple Grid States**
   - Apply different filters to CasCalls and PLS grids
   - Switch between grids multiple times
   - Verify each grid maintains its own filter state

3. **Filter Reset**
   - Apply filters to a grid
   - Reset filters
   - Navigate away and back
   - Verify no filters are applied (state cleared)

4. **Session Persistence**
   - Apply filters
   - Refresh the page
   - Verify filters are lost (expected behavior - in-memory only)

## 8. Optional Enhancements

### Local Storage Persistence (Optional)

If session persistence is desired, add localStorage integration:

```typescript
// In the store mutations, add localStorage save/load
const mutations = {
  [GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__MUTATION_SET_FILTER_STATE](
    state: IGridFilterStateStoreState,
    payload: { gridId: SocketGroup; filterState: ButtonFiltersControllerState }
  ): void {
    Vue.set(state.filterStates, payload.gridId, payload.filterState);
    // Optional: Save to localStorage
    localStorage.setItem('gridFilterStates', JSON.stringify(state.filterStates));
  },
  
  // Add initialization from localStorage
  INIT_FROM_STORAGE(state: IGridFilterStateStoreState): void {
    const saved = localStorage.getItem('gridFilterStates');
    if (saved) {
      try {
        state.filterStates = JSON.parse(saved);
      } catch (e) {
        console.warn('Failed to load grid filter states from localStorage:', e);
      }
    }
  }
};
```

## 9. Performance Considerations

- **Memory Usage**: Filter states are stored in memory only by default
- **Cleanup**: Consider adding cleanup for unused grid states
- **Deep Cloning**: Ensure proper cloning to avoid reference issues
- **Debouncing**: Consider debouncing filter state saves for rapid changes

## 10. Error Handling

- **Invalid Grid IDs**: Handle cases where gridId is undefined or invalid
- **Corrupted State**: Gracefully handle corrupted saved states
- **Migration**: Handle schema changes in filter state structure

This implementation provides a robust, type-safe solution for persistent grid filter states that integrates seamlessly with the existing CLEO architecture.