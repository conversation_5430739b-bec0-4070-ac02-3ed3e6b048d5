import Vue from "vue";
import Vuex, { Store, StoreOptions } from "vuex";

import {
  ISocketStoreState,
  SOCKET_STORE_CONST,
  socketStore
} from "@/socket/socket-store";

import {
  CONFIG_STORE_CONST,
  configStore,
  IConfigStoreState
} from "@/common/config/config-store";
import {
  IPermissionStoreState,
  PERMISSION_STORE_CONST,
  permissionStore
} from "@/permissions/permisssion-store";
import {
  IUserStoreState,
  USER_STORE_CONST,
  userStore
} from "@/user/user-store";
import {
  CONTEXT_MENU_STORE_CONST,
  contextMenuStore,
  IContextMenuStoreState
} from "@/calls/grids/contextmenu/context-menu-store";
import {
  ILoginStoreState,
  LOGIN_STORE_STORE_CONST,
  loginStore
} from "@/login/login-store";
import {
  IKeywordsStoreState,
  KEYWORD_STORE_STORE_CONST,
  keywordStore
} from "@/keywords/keywords-store";
import {
  IPaccsStoreState,
  PACCS_STORE_CONST,
  paccsStore
} from "@/paccs/paccs-store";
import {
  IGridFilterStateStoreState,
  GRID_FILTER_STATE_STORE_CONST,
  gridFilterStateStore
} from "@/calls/grids/grid-filter/grid-filter-state-store";

Vue.use(Vuex);

export interface IRootState {
  [SOCKET_STORE_CONST.SOCKET__CONST_MODULE_NAME]: ISocketStoreState;
  [CONFIG_STORE_CONST.CONFIG__CONST_MODULE_NAME]: IConfigStoreState;
  [PERMISSION_STORE_CONST.PERMISSION__CONST_MODULE_NAME]: IPermissionStoreState;
  [USER_STORE_CONST.USER__CONST_MODULE_NAME]: IUserStoreState;
  [CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__CONST_MODULE_NAME]: IContextMenuStoreState;
  [LOGIN_STORE_STORE_CONST.LOGIN_STORE__CONST_MODULE_NAME]: ILoginStoreState;
  [KEYWORD_STORE_STORE_CONST.KEYWORD_STORE__CONST_MODULE_NAME]: IKeywordsStoreState;
  [PACCS_STORE_CONST.PACCS__CONST_MODULE_NAME]: IPaccsStoreState;
  [GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME]: IGridFilterStateStoreState;
}

const store: StoreOptions<IRootState> = {
  strict: true,
  modules: {
    [SOCKET_STORE_CONST.SOCKET__CONST_MODULE_NAME]: socketStore,
    [CONFIG_STORE_CONST.CONFIG__CONST_MODULE_NAME]: configStore,
    [PERMISSION_STORE_CONST.PERMISSION__CONST_MODULE_NAME]: permissionStore,
    [USER_STORE_CONST.USER__CONST_MODULE_NAME]: userStore,
    [CONTEXT_MENU_STORE_CONST.CONTEXT_MENU__CONST_MODULE_NAME]: contextMenuStore,
    [LOGIN_STORE_STORE_CONST.LOGIN_STORE__CONST_MODULE_NAME]: loginStore,
    [KEYWORD_STORE_STORE_CONST.KEYWORD_STORE__CONST_MODULE_NAME]: keywordStore,
    [PACCS_STORE_CONST.PACCS__CONST_MODULE_NAME]: paccsStore,
    [GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME]: gridFilterStateStore
  }
};

export const appStore = new Vuex.Store<IRootState>(store);

//  Until v3, use this.
export function useStore(): Store<IRootState> {
  return appStore;
}
