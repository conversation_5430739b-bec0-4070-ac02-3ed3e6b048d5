import { HTTP_TIMEOUT_MS } from "@/test/test-config";
import { getJwtFromIdentityServer } from "@/common/identity/identity-data";

describe("Clubs", () => {
  test(
    "getJwtFromIdentityServer",
    async () => {
      //  TODO SSL cert
      //  https://stackoverflow.com/questions/51363855/how-to-configure-axios-to-use-ssl-certificate
      const response: any = await getJwtFromIdentityServer({
        grantType: "password",
        clientId: "EPSSolo_Client",
        clientSecret: "secret_for_the_epssoloclient",
        userName: "demo",
        password: "D3m0!Password",
        scope: "EPSSolo_IdentityManager_API"
      });

      expect(response).toBe(444);
    },
    HTTP_TIMEOUT_MS
  );
});
