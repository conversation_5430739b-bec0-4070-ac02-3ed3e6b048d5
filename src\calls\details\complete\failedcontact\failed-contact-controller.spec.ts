// import { useSaveAndReturnController } from "./useSaveAndReturnController";

// import * as FailedContactService from "./failed-contact-service";
import { useFailedContactController } from "./useFailedContactController";

describe("SaveAndReturn", () => {
  it("contact made", () => {
    // const state = FailedContactService.factoryFailedContactControllerState();

    const failedContactController = useFailedContactController();

    expect(failedContactController.state.currentStep).toBe(
      "END_ASSESSMENT_CONFIRMATION"
    );

    failedContactController.goto("NEXT");

    expect(failedContactController.state.validationMessages.length).toBe(0);

    expect(failedContactController.state.currentStep).toBe(
      "HOW_WAS_CASE_MANAGED"
    );

    failedContactController.goto("NEXT");
    expect(failedContactController.state.validationMessages.length).toBe(1);
    expect(failedContactController.state.validationMessages[0].id).toBe(
      "NOT_SELECTED"
    );

    failedContactController.onHowMangedSelected({
      id: "BASE",
      description: "Base - Face to Face",
      value: "3-BaseF2F"
    });

    failedContactController.goto("NEXT");
    expect(failedContactController.state.currentStep).toBe("CONTACT_MADE");

    // Trying to move on from this step...but not made selection.
    failedContactController.goto("NEXT");
    expect(failedContactController.state.validationMessages.length).toBe(1);
    expect(failedContactController.state.validationMessages[0].id).toBe(
      "NOT_SELECTED"
    );
    //  ...so staying on this step...
    expect(failedContactController.state.currentStep).toBe("CONTACT_MADE");

    failedContactController.contactMade(true);
    failedContactController.goto("NEXT");
    expect(failedContactController.state.currentStep).toBe("UNKNOWN");
    // expect(result[0].id).toBe("NOT_SELECTED");
  });

  it("contact NOT made", () => {
    // const state = FailedContactService.factoryFailedContactControllerState();

    const failedContactController = useFailedContactController();

    expect(failedContactController.state.currentStep).toBe(
      "END_ASSESSMENT_CONFIRMATION"
    );

    failedContactController.goto("NEXT");

    expect(failedContactController.state.validationMessages.length).toBe(0);

    expect(failedContactController.state.currentStep).toBe(
      "HOW_WAS_CASE_MANAGED"
    );

    failedContactController.goto("NEXT");
    expect(failedContactController.state.validationMessages.length).toBe(1);
    expect(failedContactController.state.validationMessages[0].id).toBe(
      "NOT_SELECTED"
    );

    failedContactController.onHowMangedSelected({
      id: "BASE",
      description: "Base - Face to Face",
      value: "3-BaseF2F"
    });

    failedContactController.goto("NEXT");
    expect(failedContactController.state.currentStep).toBe("CONTACT_MADE");

    // Trying to move on from this step...but not made selection.
    failedContactController.goto("NEXT");
    expect(failedContactController.state.validationMessages.length).toBe(1);
    expect(failedContactController.state.validationMessages[0].id).toBe(
      "NOT_SELECTED"
    );
    //  ...so staying on this step...
    expect(failedContactController.state.currentStep).toBe("CONTACT_MADE");

    failedContactController.contactMade(false);
    expect(failedContactController.state.userResponse.contactMade).toBe(false);

    failedContactController.goto("NEXT");
    expect(failedContactController.state.validationMessages.length).toBe(1);
    expect(failedContactController.state.validationMessages[0].id).toBe(
      "FAILED_CONTACT_REASON"
    );
    //  stays on this step...
    expect(failedContactController.state.currentStep).toBe("CONTACT_MADE");

    expect(failedContactController.state.autoProgress).toBe(true);
    failedContactController.onFailedContactReasonSelected({
      id: "NO_ANSWER",
      description: "No Answer",
      value: "NO_ANSWER"
    });
    expect(failedContactController.state.currentStep).toBe(
      "FAILED_CONTACT_SAFEGUARDING"
    );

    failedContactController.state.debug = true;
    failedContactController.goto("NEXT");
    expect(failedContactController.state.currentStep).toBe(
      "FAILED_CONTACT_SAFEGUARDING"
    );
    // expect(failedContactController.state.validationMessages[0].id).toBe(
    //   "FAILED_CONTACT_REASON_XXX"
    // );
    // expect(failedContactController.state.currentStep).toBe("CONTACT_MADE");
  });
});
