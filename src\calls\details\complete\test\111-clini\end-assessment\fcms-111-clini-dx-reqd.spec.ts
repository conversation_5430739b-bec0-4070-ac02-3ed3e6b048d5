import {
  ICompleteControllerInput,
  useCompleteController
} from "../../../useCompleteController";
import { ICallDetail } from "@/calls/details/call-details-models";
import { factoryCallDetail } from "@/calls/details/call-detail-service";
import * as CallDetailService from "@/calls/details/call-detail-service";

describe("111 Clini useCompleteController Dx validation REQUIRED", () => {
  /**
   *
   */
  it("111 Clini contact made flow", () => {
    const callDetailFcms: ICallDetail = factoryCallDetail();

    callDetailFcms.Service = {
      id: 14,
      serviceType: "111",
      name: "FCMS"
    };

    callDetailFcms.dx = {
      ch: {
        dxCode: "Dx333",
        dxDescription: ""
      },
      clini: {
        dxCode: "",
        dxDescription: ""
      }
    };

    const completeControllerInput: ICompleteControllerInput = {
      completeProcess: "COMPLETE_PROCESS",
      callDetail: callDetailFcms,
      callDetailState: CallDetailService.factoryICallDetailState(),
      userRole: "[CAS CLINI]",
      userPermissions: {
        COMPLETE_USE_111_CLINICIAN: {
          PermissionAccess: "A",
          PermissionForm: "CALL",
          PermissionAction: "COMPLETE_USE_111_CLINICIAN"
        }
      },
      dx: {
        requiringValidation: ["Dx333"]
      },
      failedContactConfig: {
        config: {
          attemptsRequired: 2,
          minsInterval: 20
        }
      },
      userRoleTypes: {
        isCasClinician: false,
        isClinician: true
      }
    };

    const controller = useCompleteController(completeControllerInput);
    controller.state.debug = false;
    expect(controller.completeControllerInput.completeProcess).toBe(
      "COMPLETE_PROCESS"
    );
    expect(controller.state.processName).toBe("COMPLETE_111_CLINICAL");

    expect(controller.state.dx.usingDx).toBe("DX333");
    expect(controller.state.dx.isDxValidationRequired).toBe(true);
    expect(controller.state.autoProgress).toBe(true);

    expect(controller.state.currentStep).toBe("END_ASSESSMENT_CONFIRMATION");

    const steps = controller.state.steps;
    const stepNames = Object.keys(steps);

    expect(stepNames.length).toBe(10);

    expect(stepNames[0]).toBe("END_ASSESSMENT_CONFIRMATION");
    expect(stepNames[1]).toBe("CONTACT_MADE");
    expect(stepNames[2]).toBe("OUTCOMES");
    expect(stepNames[3]).toBe("FAILED_CONTACT_REASON");
    expect(stepNames[4]).toBe("INSUFFICIENT_CONTACT_ATTEMPTS");
    expect(stepNames[5]).toBe("FAILED_CONTACT_RISK_ASSESSMENT");
    expect(stepNames[6]).toBe("CLINICAL_VALIDATION");
    expect(stepNames[7]).toBe("TAXI");
    expect(stepNames[8]).toBe("VULNERABILITY");
    expect(stepNames[9]).toBe("UNKNOWN");

    controller.onEndAssessmentConfirmation();
    expect(controller.state.currentStep).toBe("CONTACT_MADE");
    controller.onContactMade({
      id: "CONTACT_MADE",
      description: "Contact Made",
      value: "CONTACT_MADE"
    });

    expect(controller.state.currentStep).toBe("OUTCOMES");
    controller.onOutcomesSelected({
      outcome: "Base",
      subOutcome: "Ashford",
      otherOutcome: "",
      outcomeOptions: {
        "999": [],
        Base: ["Ashford", "Bexhill", "Camden"]
      }
    });
    expect(controller.state.currentStep).toBe("TAXI");
    controller.onTaxiSelected({
      id: "YES",
      description: "Yes",
      value: "YES"
    });

    expect(controller.state.currentStep).toBe("VULNERABILITY");
    controller.onVulnerabilitySelected({
      adult: {
        id: "YES",
        description: "Yes",
        value: "YES"
      },
      child: {
        id: "YES",
        description: "Yes",
        value: "YES"
      },
      mcaAssessed: {
        id: "NO",
        description: "No",
        value: "NO"
      },
      mcaRequired: {
        id: "NO",
        description: "No",
        value: "NO"
      }
    });
    controller.goto("NEXT");
    expect(controller.state.isProcessComplete).toBe(true);
  });
});
