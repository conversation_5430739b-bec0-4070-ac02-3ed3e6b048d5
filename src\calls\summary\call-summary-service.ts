import {
  CLEO_GRID_COLUMN_NAMES,
  ICleoCallSummary
} from "@/calls/summary/call-summarry-models";
import { ISingleQCall } from "@/socket/socket-models";

import { CleoCommonService } from "@/common/cleo-common-service/cleo-common-service";
import { CommonService } from "@/common/common-service";
import {
  CLEO_CLIENT_SERVICE_MAP,
  IAdapterPagedResponse
} from "@/common/common-models";

import { format, parseISO } from "date-fns";

const cleoCommonService: CleoCommonService = new CleoCommonService();
const commonService: CommonService = new CommonService();

export class CallSummaryService {
  public factoryCleoCallSummary(): ICleoCallSummary {
    return {
      AllViewExclude: "0",
      AllViewInclude: "",

      CallNo: 0,
      CallService: {
        Id: 0,
        Description: "",
        Type: ""
      },
      IucContract: {
        Id: 0,
        Description: "",
        Type: ""
      },
      cleoClientService: "",
      CallPostCode: "",
      // CallMf: "",
      CallGenderId: 0,
      CallNhsNo: "",

      BreachActualTime: "",
      BreachWarnActualTime: "",
      // BreachKey: "", //  Needs to go.
      ApplyBreach: false,
      BreachPriority: 0,
      BreachPreActualTime: "",
      BreachLevel1Mins: 0,

      CallWithBaseAckTime: "",
      CallUrgentYn: false,
      // CallReceivedISO: "",
      CallReceivedTime: "",
      CallArrivedTime: "",

      CallStatusValue: 9,

      IsLocked: "", //  User who has call locked

      CallForename: "",
      CallSurname: "",

      Itk111Online: false,

      ChFinalDispositionCode: "",
      ChFinalDispositionDescription: "",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",

      DispatchVehicle: "",
      WalkIn: false,

      CallCallback: 0,
      CallWarmTransferred: null,
      PdsTracedAndVerified: false,
      PDSAdminTrace: "",

      // PatientName: "", //  Not CLEO field.
      // CasePatientName: "",

      CallAddress1: "",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",

      CallAge: 0,
      CallAgeClass: "",
      CallDobIso: "",

      CallCName: "",
      CallCRel: "",

      CallCreatedBy: "",
      CallDoctorName: "",

      CallClassification: {
        Id: 0,
        Description: ""
      },
      CallSubClassification: {
        Id: 0,
        Description: ""
      },

      CallTelNo: "",
      CallTelNoAlt1: "",
      CallTelNoAltType1: "",
      CallTelNo_R: "",

      CallSymptoms: "",
      CallPractice: "",

      Call1StContact: "",
      Call1StContactPathways: "",
      PathwaysCaseId: "",

      PatientContactCode: "",
      PatientContactCodeCount: 0,
      LastFailedContactTime: "",

      CallAppointmentTime: "",
      DateAppointmentStart: "",
      CareConnectAppointmentStart: "",
      AFT_UNABLE_REASON: "",

      ComfortSentServiceTime: "",
      ComfortSmsTime: "",

      ComfortSentService2Time: "",
      ComfortSmsTime2: "",

      CourtesyTime: "",
      CourtesyUser: "",
      CourtesyCount: 0,
      CourtesyContact: false,

      CliniHighPriority: false,

      CallInformationalOutcomes: "",
      CallInformationalSubOutcomes: "",
      CallInformationalOutcomesComment: "",

      DutyBase: "",
      StartConsultationPerformed: false,

      CasValidationCount: 0,
      CasValidationUser: "",
      CasValidationTime: "",
      CasValidationReason: "",

      CaseComments: "",
      COMPLETE_PREVENT: "0",
      LinkedCallID: "",
      FOLLOW_UP_URGENT: "0",
      FOLLOW_UP_Active: null,
      Call_HCP: "",
      OversightValidationType: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",

      SMS_HAS: false,
      SMS_SENT: false,
      SMS_LATEST_AT: "",
      SMS_LATEST_USER: "",
      SMS_LATEST_MESSAGE: "",

      Cpl_supportTypeRequired: "",

      GOODSAM_IMAGE_STATUS: "",
      CallFAction: "",
      Cpl_furtherActionGPText: "",

      PLS_REASON: "",
      PLS_TIME: "",
      PLS_USER: "",
      PLS_ACTION: "",
      PLS_ACTIONTEXT: ""
    };
  }

  public factoryAdapterGridResponse<T>(): IAdapterPagedResponse<T> {
    return {
      CurrentPage: 0,
      RecordsPerPage: 10,
      TotalPages: 0,
      TotalRecords: 0,
      Records: []
    };
  }

  public mapSingleQCallToCleoCallSummary(
    singleQCall: ISingleQCall,
    cleoCallSummary: ICleoCallSummary
  ): ICleoCallSummary {
    return {
      ...cleoCallSummary,
      IsLocked: singleQCall.CaseLocked ? "Locked" : ""
    };
  }

  public getTown(cleoCallSummary: ICleoCallSummary): string {
    if (cleoCallSummary.CallTown !== "") {
      return cleoCallSummary.CallTown;
    } else if (cleoCallSummary.CallAddress4 !== "") {
      return cleoCallSummary.CallAddress4;
    } else if (cleoCallSummary.CallAddress3 !== "") {
      return cleoCallSummary.CallAddress3;
    } else if (cleoCallSummary.CallAddress2 !== "") {
      return cleoCallSummary.CallAddress2;
    } else if (cleoCallSummary.CallAddress1 !== "") {
      return cleoCallSummary.CallAddress1;
    }
    return "";
  }

  public isRealCall(cleoCallSummary: ICleoCallSummary): boolean {
    return Number(cleoCallSummary.CallStatusValue) > 0;
  }

  public getService(cleoCallSummary: ICleoCallSummary): string {
    const callService = cleoCommonService.serviceNameMask(
      cleoCallSummary.CallService.Description
        ? cleoCallSummary.CallService.Description
        : ""
    );

    const iucContract =
      cleoCallSummary.IucContract &&
      cleoCallSummary.IucContract.Description &&
      cleoCallSummary.IucContract.Description.length > 0
        ? cleoCommonService.serviceNameMask(
            cleoCallSummary.IucContract.Description
          )
        : "";

    const cleoClientService = cleoCallSummary.cleoClientService;
    if (cleoClientService && cleoClientService.length > 0) {
      const label = CLEO_CLIENT_SERVICE_MAP[cleoClientService]
        ? CLEO_CLIENT_SERVICE_MAP[cleoClientService]
        : cleoClientService;
      return callService + ": " + label;
    }

    return callService;
  }

  public getDxCodeTooltip(cleoCallSummary: ICleoCallSummary): string {
    let dxCodeCH = "";
    let dxCodeFinal = "";
    // let dxDescription = "";
    let dxDescriptionCH = "";
    let dxDescriptionFinal = "";

    /*
      "CHFinalDispositionCode": "P6h",
  "CHFinalDispositionDescription": "Speak to a local service within 1 hour",
  "FinalDispositionCode": "P2h",
  "FinalDispositionDescription": "Speak to a local service within 1 hour",
     */

    dxCodeCH = cleoCallSummary.ChFinalDispositionCode
      ? cleoCallSummary.ChFinalDispositionCode
      : "";
    dxDescriptionCH = cleoCallSummary.ChFinalDispositionDescription
      ? cleoCallSummary.ChFinalDispositionDescription
      : "";

    dxCodeFinal = cleoCallSummary.FinalDispositionCode
      ? cleoCallSummary.FinalDispositionCode
      : "";
    dxDescriptionFinal = cleoCallSummary.FinalDispositionDescription
      ? cleoCallSummary.FinalDispositionDescription
      : "";

    // const dxCh = dxCodeCH + ": " + dxDescriptionCH;
    // const dxFinal = dxCodeFinal + ": " + dxDescriptionFinal;

    const dxCh =
      dxCodeCH.length === 0 ? "" : "[CH] " + dxCodeCH + ": " + dxDescriptionCH;

    const dxFinal =
      dxCodeFinal.length === 0
        ? ""
        : " [Final] " + dxCodeFinal + ": " + dxDescriptionFinal;

    return dxCh + dxFinal;
  }

  public getDxCode(cleoCallSummary: ICleoCallSummary): string {
    let dxCode = "";
    let dxCodeCH = "";
    let dxCodeFinal = "";
    dxCodeCH = cleoCallSummary.ChFinalDispositionCode
      ? cleoCallSummary.ChFinalDispositionCode
      : "";
    dxCodeFinal = cleoCallSummary.FinalDispositionCode
      ? cleoCallSummary.FinalDispositionCode
      : "";
    dxCode = dxCodeFinal.length === 0 ? dxCodeCH : dxCodeFinal;
    // dxCode = dxCodeCH + " : " + dxCodeFinal;
    return dxCode.toUpperCase();
  }

  public timeHumanShort(timePeriodInSeconds: number): string {
    let days = 0;
    let hrs = 0;
    let mins = 0;

    const secsInMin = 60;
    const secsInHour = 3600;
    const secsInDay = 86400;
    const secsInMonth = 2678400; //	Approx, 31 days
    const secsInYear = 31536000; //	Approx, 365 days.

    if (timePeriodInSeconds < secsInMin) {
      return "<1m";
    } else if (timePeriodInSeconds < secsInHour) {
      mins = Math.floor(timePeriodInSeconds / secsInMin);
      return mins + "m";
    } else if (timePeriodInSeconds < secsInDay) {
      hrs = Math.floor(timePeriodInSeconds / secsInHour);
      mins = Math.floor(
        Number((timePeriodInSeconds % secsInHour).toFixed(0)) / secsInMin
      );
      return hrs + "h " + mins + "m";
    } else if (timePeriodInSeconds < secsInMonth) {
      days = Math.floor(timePeriodInSeconds / secsInDay);
      hrs = Math.floor(
        Number((timePeriodInSeconds % secsInDay).toFixed(0)) / secsInHour
      );
      return days + "d " + hrs + "h";
    } else if (timePeriodInSeconds < secsInYear) {
      return Math.floor(timePeriodInSeconds / secsInMonth) + "mt";
    } else {
      return Math.floor(timePeriodInSeconds / secsInYear) + "y";
    }
  }

  public getDefaultColumnNames(): string[] {
    return Object.keys(CLEO_GRID_COLUMN_NAMES);
  }

  public getPatientName(cleoCallSummary: ICleoCallSummary): string {
    return cleoCallSummary.CallSurname + ", " + cleoCallSummary.CallForename;
  }

  public getDOB(cleoCallSummary: ICleoCallSummary): string {
    // return as DD/MM/YYYY
    try {
      if (!cleoCallSummary.CallDobIso) {
        return "";
      }
      return format(parseISO(cleoCallSummary.CallDobIso), "dd/MM/yyyy");
    } catch (e) {
      console.log(e);
      console.log("getDOB: ", cleoCallSummary);
      return "";
    }
  }

  public getClassification(cleoCallSummary: ICleoCallSummary): string {
    // if Description has "Nurse Advice" in it, then set to "Advice"
    if (!cleoCallSummary.CallClassification.Description) {
      return "";
    }
    let description = cleoCallSummary.CallClassification.Description;
    if (description.toUpperCase() === "NURSE ADVICE") {
      description = "Advice";
    }

    //  if call sub classification is present, add it in brackets
    const callSubClassification =
      cleoCallSummary.CallSubClassification.Description;
    if (callSubClassification) {
      description += " (" + callSubClassification + ")";
    }

    //  Is non clinical support required.
    if (
      cleoCallSummary.Cpl_supportTypeRequired &&
      cleoCallSummary.Cpl_supportTypeRequired.length > 0
    ) {
      //  in all the to-ing and fro-ing/spec changes and other total flow
      // craziness, callSubClassification and CallSubClassificationManual on back end
      // are not always in sync and sometimes empty.  Sooo....
      if (callSubClassification === "") {
        description += " (Non Clinical Complete)";
      }

      if (cleoCallSummary.Cpl_supportTypeRequired === "TAKE_LIST") {
        description += " - Take List";
      } else if (
        cleoCallSummary.Cpl_supportTypeRequired === "CHILDREN_ED_REFERRAL"
      ) {
        description += " - ED Referral";
      } else {
        description += " - " + cleoCallSummary.Cpl_supportTypeRequired;
      }
    }

    if (
      cleoCallSummary.OVERSIGHT_BASE_TRIAGE_TYPE &&
      cleoCallSummary.OVERSIGHT_BASE_TRIAGE_TYPE.length > 0
    ) {
      description +=
        " -- " + cleoCallSummary.OVERSIGHT_BASE_TRIAGE_TYPE.replace("?", "");
    }

    return description;
  }

  public getPatientAge(cleoCallSummary: ICleoCallSummary): string {
    return cleoCallSummary.CallAge + cleoCallSummary.CallAgeClass;
  }

  public getSymptoms(cleoCallSummary: ICleoCallSummary): string {
    let symptoms = cleoCallSummary.CallSymptoms;
    if (!symptoms) {
      return "";
    }
    symptoms = cleoCommonService.escapeFormattedText(symptoms);
    return cleoCommonService.unescapeFormattedText(symptoms);
  }

  public getAddress(
    cleoCallSummary: ICleoCallSummary,
    joinWith?: string
  ): string | string[] {
    const init: string[] = [];

    const lineProps: (keyof ICleoCallSummary)[] = [
      "CallAddress1",
      "CallAddress2",
      "CallAddress3",
      "CallAddress4",
      "CallPostCode"
    ];

    let lines = lineProps.reduce((accum, propName: keyof ICleoCallSummary) => {
      const propValue = cleoCallSummary[propName];
      if (propValue && propValue.toString().length > 0) {
        accum.push(propValue.toString());
      }
      return accum;
    }, init);

    lines.push(cleoCallSummary.CallPostCode);

    //  unique
    lines = [...new Set(lines)];
    return joinWith ? lines.join(joinWith) : lines;
  }

  public getCallInArray(
    cleoCallSummary: ICleoCallSummary,
    cleoCallSummaries: ICleoCallSummary[]
  ): ICleoCallSummary | null {
    const pred = (cleoCall: ICleoCallSummary): boolean => {
      return cleoCall.CallNo === cleoCallSummary.CallNo;
    };
    return commonService.findFirst(pred, cleoCallSummaries);
  }

  public isCallLockedByAnotherUser(
    cleoCallSummary: ICleoCallSummary,
    userName: string
  ): boolean {
    let lockUserName =
      cleoCallSummary && cleoCallSummary.IsLocked.length > 0
        ? cleoCallSummary.IsLocked
        : "";
    if (lockUserName.length === 0) {
      return false;
    }
    lockUserName = cleoCommonService.formatUserDominoName(lockUserName, "CN");

    const userNameCN = cleoCommonService.formatUserDominoName(userName, "CN");
    return lockUserName !== userNameCN;
  }

  public applyBreachToCall(cleoCallSummary: ICleoCallSummary): boolean {
    return !!(
      cleoCallSummary.ApplyBreach &&
      cleoCallSummary.BreachActualTime &&
      cleoCallSummary.BreachActualTime.length > 0
    );
  }
}
