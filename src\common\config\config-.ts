import { ToastOptions } from "vue-toasted";
import { ITokenResponse } from "@/login/login-models";
import { ILegacyAction } from "@/app-models";
import { ICallDetail } from "@/calls/details/call-details-models";
import { vehiclesMockData } from "@/vehicles/vehicles-mock-data";
import { ILegacyDojoResponse } from "@/common/cleo-legacy-models";
import { IVehicleLegacy } from "@/vehicles/vehicles-models";
import { userPermissionServerResponseMockData } from "@/permissions/permission-mock-data";
import {
  ICallDetailLegacy,
  ICallDetailLegacyFieldData
} from "@/calls/details/call-details-legacy-models";
import { MOCK_PATHWAYS_DATASET_LEGACY } from "@/common/config/config-mock";
import { mock_BRISDOC_OP_NAV_SUPPORT } from "@/permissions/brisdoc/mock_BRISDOC_OP_NAV_SUPPORT";

if (process.env.NODE_ENV === "development" || process.env.NODE_ENV === "test") {
  window.dxValidation = "Dx325, Dx333444";

  window.MyGlobalSession = {
    UserNameCN: "Nick Wall2",
    Global_DB_Paths: {
      HOST_PATH: process.env.VUE_APP_CLEO_BASE_URL,
      PATH_CALL: process.env.VUE_APP_CLEO_CALL_DB,
      FULL_PATH: process.env.VUE_APP_CLEO_FULL_PATH,
      PATH_XCLEO: process.env.VUE_APP_XCLEO_FULL_PATH,
      PATH_GLOBAL: process.env.VUE_APP_PATH_GLOBAL
    },
    SOCKET_URL: process.env.VUE_APP_SOCKET_URL,
    PACCS_URL: process.env.VUE_APP_PACCS_URL,
    ADAPTER_URL: process.env.VUE_APP_ADAPTER_URL,
    LOG_JS_ERRORS: process.env.VUE_APP_LOG_JS_ERRORS as "YES" | "NO",
    LOG_JS_ERRORS_PATH: process.env.VUE_APP_LOG_JS_ERRORS_PATH,
    GOOGLE_API_KEY: process.env.VUE_APP_GOOGLE_MAP_API_KEY as string,
    WHAT3WORDS_API_KEY: process.env.VUE_APP_WHAT3WORDS_API_KEY as string,
    GRID_LONG_POLLING_SECS: 3,
    DAB_URL: process.env.VUE_APP_DAB_URL,
    GRID_BREACH_REFRESH_SECS: 60,
    sesui: {
      enabled: true,
      url: process.env.VUE_APP_SESUI_URL as string,
      user: process.env.VUE_APP_SESUI_USER as string,
      pw: process.env.VUE_APP_SESUI_PW as string
    }
  };

  const mockJwt: ITokenResponse = {
    access_token: process.env.VUE_APP_JWT as string,
    expires_in: 3600,
    token_type: "Bearer",
    scope: "api1"
  };

  window.ApplicationControllerClient = {
    getUserConfigRole: function() {
      return "[Role Disp Cont]";
    },
    getSsoJwt: function() {
      return null;
    },
    requestGenericJwt: function() {
      return Promise.resolve(mockJwt);
    },
    createActionEventLog: function(
      category: string,
      action: string,
      data?: Record<string, string | string[]>[]
    ) {
      console.log(
        "createActionEventLog: " + category + ", " + action + ", " + data
      );
    }
  };

  window.CGC = {
    clickItem: null
  };

  window.ADAPTER_MENU = {
    id: ""
  };

  window.ADAPTER_CLEO_ACTION = {
    payload: {
      actionType: "",
      data: null
    } as ILegacyAction
  };

  window.adapterMenu = {
    state: {
      showAdapter: true,
      triggerResize: {
        timeIso: ""
      }
    }
  };

  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  window.callSummaryDisplay = CallControllerClient => {
    function selectDraft111Call() {
      console.warn("callSummaryDisplay.selectDraft111Call()...local");
      return;
    }

    return {
      selectDraft111Call
    };
  };

  /**
   * This is not correct architecture, but loads locally on call-detail-route
   */
  const JSONFields: ICallDetailLegacy = {
    CallDOB: { VALUE: ["19801001T090101,00+00"] },
    CallMF: { VALUE: ["Male"] },
    CallTelNo_R: { VALUE: ["07912626865"] },
    ContainsRehydratedCase: { VALUE: [""] },
    PathwaysCaseId: { VALUE: ["Joe"] },
    PathwaysCaseId_FROM_ITK: { VALUE: [""] },
    PathwaysCaseId_ITK_IN: { VALUE: [""] },
    CallForename: { VALUE: ["Joe"] },
    CallSurname: { VALUE: ["ConfigFileBloggs"] },
    CallNo: { VALUE: ["987635"] },
    CallAddress1: { VALUE: ["St Clements"] },
    CallAddress2: { VALUE: ["King st"] },
    CallAddress3: { VALUE: ["Colyton"] },
    CallAddress4: { VALUE: [""] },
    CallTown: { VALUE: ["Colyton"] },
    CallPostCode: { VALUE: ["EX24 6LF"] },
    CallClassification: { VALUE: ["Advice"] },
    CallAge: { VALUE: ["33"] },
    CallAgeClass: { VALUE: ["Yrs"] },
    CallSymptoms: {
      VALUE: ["Patient feels sick and needs to blah, blah"]
    },
    Dispatch_Vehicle: { VALUE: [""] },
    CallUrgentYn: { VALUE: ["No"] },
    CareHomeId: { VALUE: ["111"] },
    CareHomeName: { VALUE: ["Care Home 111"] },
    USER_CONFIG: mock_BRISDOC_OP_NAV_SUPPORT, // userPermissionServerResponseMockData
    // CallService: { VALUE: ["Norfolk and Wisbech 111"] },
    // CallServiceId: { VALUE: ["3"] },
    CallService: { VALUE: ["CAS"] },
    CallServiceId: { VALUE: ["99"] },
    CallServiceType: { VALUE: ["CAS"] },
    IUC_Contract: { VALUE: [""] },
    IUC_ContractID: { VALUE: ["14"] },
    cleoClientService: { VALUE: ["FRAILTY"] },
    PatientContactCode_Events: {
      VALUE: [
        "CN=Hannah Spicer/O=sehnp~Answerphone - Message left~2025-04-01T14:49:00",
        "CN=Hannah Spicer/O=sehnp~No Answer~2024-06-28T02:29:46",
        "CN=Hannah Spicer/O=sehnp~Answerphone - Message left~2024-06-28T02:48:21"
        // "CN=Hannah Spicer/O=sehnp~Answerphone - Message left~2024-06-28T04:03:52",
        // "CN=Hannah Spicer/O=sehnp~Answerphone - Message left~2024-06-28T04:07:21"
      ]
    },

    CHFinalDispositionCode: { VALUE: ["Dx325"] },
    CHFinalDispositionDescription: {
      VALUE: [
        "Speak to a Clinician from our service Immediately - Toxic Ingestion/Inhalation"
      ]
    },
    FinalDispositionCode: { VALUE: ["Dx325"] },
    FinalDispositionDescription: { VALUE: [""] },

    Cpl_Action: { VALUE: [""] },
    Cpl_managedHow: { VALUE: [""] },
    Cpl_contactMade: { VALUE: [""] },
    Cpl_outcome: { VALUE: [""] },
    Cpl_outcomeSub: { VALUE: [""] },
    Cpl_failedContactReason: { VALUE: [""] },
    Cpl_patientRiskAssessment: { VALUE: [""] },
    Cpl_patientRiskAssessmentAction: { VALUE: [""] },
    Cpl_insufficientContactAttempts: { VALUE: [""] },
    Cpl_exitReason: { VALUE: [""] },
    Cpl_failedContactWarning: { VALUE: [""] },
    Cpl_onwardReferral: { VALUE: [""] },
    Cpl_onwardReferralText: { VALUE: [""] },
    Cpl_furtherActionGP: { VALUE: [""] },
    Cpl_furtherActionGPText: { VALUE: [""] },
    Cpl_clinicalValidation: { VALUE: [""] },

    Cpl_taxi: { VALUE: [""] },
    Cpl_vulnerabilityAdult: { VALUE: [""] },
    Cpl_vulnerabilityChild: { VALUE: [""] },
    Cpl_mcaAssessed: { VALUE: [""] },
    Cpl_mcaRequired: { VALUE: [""] },

    Cpl_otherOutcome: { VALUE: [""] },
    CPL_READCODES: { VALUE: [""] },

    Cpl_medicationIssuedFromStock: { VALUE: [""] },
    Cpl_nonClinicalSupportToComplete: { VALUE: [""] },
    Cpl_mhClinicianSignOffRequired: { VALUE: [""] },
    Cpl_supportTypeRequired: { VALUE: [""] },
    Cpl_supportTypeRequiredComments: { VALUE: [""] },

    Cpl_AuditQuestions: { VALUE: [""] },

    Cpl_nonClinicalReason: { VALUE: [""] },
    Cpl_nonClinicalReasonComment: { VALUE: [""] },

    CallInformationalOutcomes: { VALUE: [""] },
    CallInformationalSubOutcomes: { VALUE: [""] },
    CallInformationalOutcomesComment: { VALUE: [""] },

    flag_complete: { VALUE: ["0"] }
  };

  // PatientContactCode_Events: {
  // VALUE: [
  //   "CN=Hannah Spicer/O=sehnp~Answerphone - Message left~2024-06-28T01:49:00",
  //   "CN=Hannah Spicer/O=sehnp~No Answer~2024-06-28T02:29:46",
  //   "CN=Hannah Spicer/O=sehnp~Answerphone - Message left~2024-06-28T02:48:21",
  //   "CN=Hannah Spicer/O=sehnp~Answerphone - Message left~2024-06-28T04:03:52",
  //   "CN=Hannah Spicer/O=sehnp~Answerphone - Message left~2024-06-28T04:07:21"
  // ]

  JSONFields["USER_CONFIG"] = userPermissionServerResponseMockData;

  window.CallControllerClient = {
    m_call_id: "123456",
    isNewCall: false,
    serviceDoc: {
      serviceId: 3,
      serviceName: "Norfolk and Wisbech 111",
      serviceType: "111"
    },
    StartedConsult: false,
    paccs: {
      isReady: false,
      hasStarted: false
    },
    UserNameAbbrev: "Test User/sehnp",
    areWeInViewGrid: () => {
      return true;
    },
    getFieldValue: (fieldName: string) => {
      const mockMap: Record<string, unknown> = {
        PathwaysCaseId: "38e50f76-a1d4-4a4e-5da5-08d9574d0ae9",
        CallMF: "Male",
        CallDOB: new Date(2015, 1, 1)
      };
      return mockMap[fieldName] ? mockMap[fieldName] : "";
    },
    createNHSPathwaysDataSet: () => {
      return MOCK_PATHWAYS_DATASET_LEGACY;
    },
    hasPermission: (persmissionName: string) => {
      return true;
    },
    submitDocumentPromise: (
      callIdentifier: string | number,
      closeTheCall: boolean,
      fieldMapOverride?: Record<string, any>
    ) => {
      return Promise.resolve();
    },
    createPaccsConsultDataForPathways: () => {
      return "Local DEBUG consult data";
    },
    getValueFromCallJson: (fieldName: keyof ICallDetail) => {
      const mockMap: Record<string, unknown> = {
        PathwaysCaseId_FROM_ITK: "38e50f76-a1d4-4a4e-5da5-08d9574d0ae9",
        ContainsRehydratedCase: "1"
      };
      return mockMap[fieldName] ? mockMap[fieldName] : "";
    },
    setFieldValue: (fieldName: keyof ICallDetail, value: unknown) => {
      const ele: HTMLInputElement = document.getElementById(
        fieldName
      ) as HTMLInputElement;
      if (ele) {
        ele.value = (value as string).toString();
      }
    },
    submitSimpleFields: (
      payload: Partial<Record<keyof ICallDetail, unknown>>
    ) => {
      return Promise.resolve();
    },
    getCallDob: () => {
      return window.CallControllerClient.getFieldValue("CallDOB");
    },
    createConsultFromCurrentHubCallForPathways: () => {
      return {
        Priority: "a",
        CallDetails: "b",
        CallObjective: "c",
        CallAssessment: "d",
        CallPlan: "e"
      };
    },
    getDispatchCarDataStore: callBack => {
      setTimeout(() => {
        callBack(
          (vehiclesMockData as any) as ILegacyDojoResponse<IVehicleLegacy[]>
        );
      }, 500);
    },
    getConsultPathwaysOutput: consult => {
      return (
        "CallDetails: " +
        consult.CallDetails +
        "Priority: " +
        consult.Priority +
        "CallObjective: " +
        consult.CallObjective +
        "CallAssessment: " +
        consult.CallAssessment +
        "CallPlan: " +
        consult.CallPlan +
        " now: " +
        new Date().toISOString()
      );
    },
    initPathwaysPermissions: () => {
      console.log(
        "CallControllerClient.initPathwaysPermissions()  mocked call."
      );
    },
    addFieldToModel: (
      fieldName: keyof ICallDetailLegacy,
      fieldValue: unknown
    ) => {
      console.warn(
        "CallControllerClient.addFieldToModel()  mocked call: " +
          fieldName +
          " with value: " +
          fieldValue +
          " added."
      );
    },
    userPermissions: userPermissionServerResponseMockData,
    JSONFields: JSONFields
  } as typeof CallControllerClient;

  window.NHSPathwaysSEHUrl = "https://staging.pathways.sehnp.nhs.uk";
}

/**
 *
 */
export const CLEO_CONFIG = {
  CLEO: {
    BASE_URL: window.MyGlobalSession.Global_DB_Paths.HOST_PATH,

    CALL_DB:
      window.MyGlobalSession.Global_DB_Paths.HOST_PATH +
      "/" +
      window.MyGlobalSession.Global_DB_Paths.PATH_CALL,

    FULL_PATH: window.MyGlobalSession.Global_DB_Paths.FULL_PATH,
    XCLEO_PATH: window.MyGlobalSession.Global_DB_Paths.PATH_XCLEO,
    ERROR_LOG_PATH:
      window.MyGlobalSession.Global_DB_Paths.HOST_PATH +
      "/" +
      window.MyGlobalSession.LOG_JS_ERRORS_PATH
  },
  ADAPTER_URL: window.MyGlobalSession.ADAPTER_URL,
  SOCKET_URL: window.MyGlobalSession.SOCKET_URL,
  PACCS_URL: window.MyGlobalSession.PACCS_URL,
  DAB_URL: window.MyGlobalSession.DAB_URL
};

// console.log("CLEO_CONFIG", CLEO_CONFIG);

export const defaultErrorToastOptions: ToastOptions = {
  position: "bottom-right",
  duration: 3000,
  type: "error",
  theme: "toasted-primary"
};
