// const keys: (keyof IKeywordsRefData)[] = Object.keys(keywordsRefData) as (keyof IKeywordsRefData)[];
// type itemPreview = Pick<User, "name" | "age">

// const user = {
//   name: "jon",
//   age: 22,
//   address: {
//     linre1: "2123213"
//   }
// } as const;  // The ...as const bit make it immutable!!

//  For types

/////////////////////////
// const user = {
//   name: "bob",
//   age: 33
// };
//
// type User = typeof user;

// export const PaccsAgeGroups = [
//   "Adult",
//   "Child",
//   "Toddler",
//   "Infant",
//   "Neonate"
// ] as const;
// export type PaccsAgeGroup = typeof PaccsAgeGroups[number];

//  Function taking partial object keys
// submitSimpleFields: (
//   payload: Partial<Record<keyof ICallDetail, unknown>>
// ) => Promise<ICleoServerResponse>;
// ===============
//
export const payGrades = {
  low: "1",
  average: "2",
  high: "3"
} as const;

type t = typeof payGrades;
type payGradeType = keyof t; // 'low' | 'average' | 'high'
type payValueType = t[keyof t]; // '1' | '2' | '3'

const hisPay: payValueType = "3"; //okay
// const myPay:  payValueType = '4'; // error
