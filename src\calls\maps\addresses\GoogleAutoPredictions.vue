<template>
  <div>
    <div class="google-auto-predictions--input-wrapper">
      <div class="google-auto-predictions--input-div cleo-force-inline-block">
        <input
          class="google-auto-predictions--input"
          :class="
            isWordLongEnough ? '' : 'google-auto-predictions--input-invalid'
          "
          v-model="autoSuggestInternal"
          v-on:keyup="debounceSearch"
          placeholder="Search google maps..."
        />
      </div>

      <div
        class="google-auto-predictions--input-clear-suggestions cleo-force-inline-block"
      >
        <a href="#" v-on:click.prevent="clearUi">X</a>
      </div>
    </div>
    <div v-if="showResults" class="google-auto-predictions--suggestions">
      <div
        v-for="prediction in predictions"
        :key="prediction.place_id"
        class="google-auto-predictions--suggestion-content"
        v-on:click="suggestionSelected(prediction)"
      >
        <div>
          <span
            class="google-auto-predictions--location-context"
            v-text="prediction.description"
          ></span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch
} from "@vue/composition-api";
import { debounce } from "@/common/debounce";
// eslint-disable-next-line no-undef
import QueryAutocompletePrediction = google.maps.places.QueryAutocompletePrediction;
export default defineComponent({
  name: "google-auto-predictions",
  props: {
    autoSuggest: {
      type: String,
      default: () => {
        return "";
      }
    },
    predictions: {
      type: Array as PropType<QueryAutocompletePrediction[]>,
      default: () => {
        return [];
      }
    }
  },
  setup(
    props: {
      autoSuggest: string;
      predictions: QueryAutocompletePrediction[];
    },
    context: SetupContext
  ) {
    const autoSuggestInternal = ref(props.autoSuggest);
    const showResults = ref(false);

    const debounceSearch = debounce(() => {
      findSuggestions();
    }, 250);

    watch(
      () => props.autoSuggest,
      (newValue: string) => {
        if (autoSuggestInternal.value !== newValue) {
          autoSuggestInternal.value = newValue;
        }
      }
    );

    function suggestionSelected(prediction: QueryAutocompletePrediction) {
      showResults.value = false;
      context.emit("suggestionSelected", prediction);
    }

    function findSuggestions() {
      showResults.value = true;
      context.emit("findSuggestions", autoSuggestInternal.value);
    }

    const isWordLongEnough = computed<boolean>(() => {
      return (
        autoSuggestInternal.value.length === 0 ||
        autoSuggestInternal.value.length > 2
      );
    });

    function clearUi() {
      showResults.value = false;
      context.emit("clearSuggestions");
    }

    return {
      autoSuggestInternal,
      showResults,
      debounceSearch,
      suggestionSelected,
      isWordLongEnough,
      clearUi
    };
  }
});
</script>

<style>
.google-auto-predictions--input-invalid {
  background-color: #f3b5b5;
}

.google-auto-predictions--input-wrapper {
  width: 100%;
  /*border: 1px solid black;*/
}

.google-auto-predictions--input-div {
  width: 98%;
}

.google-auto-predictions--input {
  width: 100%;
  line-height: 2;
  font-weight: 600;
}

.google-auto-predictions--input-clear-suggestions {
  width: 0%;
  /*padding-left: 5px;*/
  position: relative;
  left: -20px;
}

.google-auto-predictions--drop {
}

.google-auto-predictions--suggestions {
  border-left: 1px solid #e1e1e1;
  border-right: 1px solid #e1e1e1;
  border-bottom: 1px solid #e1e1e1;
}

.google-auto-predictions--suggestion-content {
  font-size: 17px;
  font-family: Source Sans Pro, sans-serif !important;
  /*height: 68px;*/
  padding-left: 16px;
  padding-right: 16px;
  border-top: 1px solid #e1e1e1;
}

.google-auto-predictions--suggestion-content:hover {
  text-decoration: none;
  background-color: rgba(0, 0, 0, 0.04);
}

.google-auto-predictions--suggestion-logo {
  color: #e11f26;
  font-weight: 600;
}

.google-auto-predictions--location-context {
  /*transform: translateY(-4%);*/
  font-weight: 600;
  line-height: 2;
}

.google-auto-predictions--nearest-place {
  font-size: 13px;
  color: #525252;
  font-weight: 400;
  line-height: 17px;
}

.google-auto-predictions--near {
  margin-right: 5px;
}
</style>
