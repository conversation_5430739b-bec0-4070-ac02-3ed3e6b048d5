import { PermissionService } from "@/permissions/permission-service";
import {
  ICleoPermission,
  IJwtClaims,
  PERMISSION_NAMES
} from "@/permissions/permission-models";
import { mockLiveOutput } from "@/permissions/permission-mock-data";

const permissionService: PermissionService = new PermissionService();

describe("PermissionService", () => {
  it("normalizePerms", () => {
    const perms = ({
      ["Call.First Contact"]: {
        PermissionForm: "Call",
        PermissionAccess: "A",
        PermissionAction: "First Contact"
      },
      ["Call.Make Appointment"]: {
        PermissionForm: "Call",
        PermissionAccess: "A",
        PermissionAction: "Make Appointment"
      },
      ["Call.Acknowledge Call On Open"]: {
        PermissionForm: "Call",
        PermissionAccess: "A",
        PermissionAction: "Acknowledge Call On Open"
      }
    } as any) as Record<string, ICleoPermission>;

    const config = permissionService.upperCasePerms(perms);
    expect(config["CALL.MAKE APPOINTMENT"].PermissionAction).toBe(
      "MAKE APPOINTMENT"
    );
  });

  it("getUserPermission", () => {
    const perms = ({
      ["Call.First Contact"]: {
        PermissionForm: "Call",
        PermissionAccess: "A",
        PermissionAction: "First Contact"
      },
      ["Call.Make Appointment"]: {
        PermissionForm: "Call",
        PermissionAccess: "A",
        PermissionAction: "Make Appointment"
      },
      ["Call.Acknowledge Call On Open"]: {
        PermissionForm: "Call",
        PermissionAccess: "A",
        PermissionAction: "Acknowledge Call On Open"
      }
    } as any) as Record<string, ICleoPermission>;

    expect(
      permissionService.getUserPermission(
        perms,
        PERMISSION_NAMES.MAKE_APPOINTMENT
      )?.PermissionAction
    ).toBe(PERMISSION_NAMES.MAKE_APPOINTMENT);

    expect(permissionService.getUserPermission(perms, "BOGUS_PERM_NAME")).toBe(
      null
    );

    expect(
      permissionService.getUserPermission(
        perms,
        PERMISSION_NAMES.MAKE_APPOINTMENT,
        "CALL"
      )?.PermissionAction
    ).toBe(PERMISSION_NAMES.MAKE_APPOINTMENT);

    expect(
      permissionService.getUserPermission(
        perms,
        PERMISSION_NAMES.MAKE_APPOINTMENT,
        "WEBUI_DOCVIEW"
      )
    ).toBe(null);

    expect(
      permissionService.getUserPermission(perms, "MAKE Appointment", "CALL")
        ?.PermissionAction
    ).toBe(PERMISSION_NAMES.MAKE_APPOINTMENT);
  });

  it("simpleKeyPerms", () => {
    const perms = ({
      ["Call.First Contact"]: {
        PermissionForm: "Call",
        PermissionAccess: "A",
        PermissionAction: "First Contact".toUpperCase()
      },
      ["Call.Make Appointment"]: {
        PermissionForm: "Call",
        PermissionAccess: "A",
        PermissionAction: "Make Appointment".toUpperCase()
      },
      ["Call.Acknowledge Call On Open"]: {
        PermissionForm: "Call",
        PermissionAccess: "A",
        PermissionAction: "Acknowledge Call On Open".toUpperCase()
      }
    } as any) as Record<string, ICleoPermission>;

    const config = permissionService.simpleKeyPerms(perms);
    expect(config["MAKE APPOINTMENT"].PermissionAction).toBe(
      "MAKE APPOINTMENT"
    );

    expect(config["CALL.MAKE APPOINTMENT"]).toBe(undefined);
  });

  it("getUserTimeGroupPermissions", () => {
    const perms = ({
      ["Call.First Contact"]: {
        PermissionForm: "Call",
        PermissionAccess: "A",
        PermissionAction: "First Contact".toUpperCase()
      },
      ["Call.Make Appointment"]: {
        PermissionForm: "Call",
        PermissionAccess: "A",
        PermissionAction: "Make Appointment".toUpperCase()
      },
      ["Call.Acknowledge Call On Open"]: {
        PermissionForm: "Call",
        PermissionAccess: "A",
        PermissionAction: "Acknowledge Call On Open".toUpperCase()
      }
    } as any) as Record<string, ICleoPermission>;

    const result = permissionService.getUserTimeGroupPermissions(perms);
    expect(result[PERMISSION_NAMES.MAKE_APPOINTMENT].PermissionAction).toBe(
      PERMISSION_NAMES.MAKE_APPOINTMENT
    );

    expect(result[PERMISSION_NAMES.ACKNOWLEDGE_RECEIPT_BASE]).toBe(undefined);
  });

  it("getJwtTokenClaims", () => {
    const claims = ({
      nbf: **********,
      exp: **********,
      iss: "https://identityserver.sta.apps.ic24.nhs.uk:778",
      aud: [
        "https://identityserver.sta.apps.ic24.nhs.uk:778/resources",
        "api1"
      ],
      client_id: "ro.client",
      sub: "38d77199-c529-42df-b7f0-ebed0d831329",
      auth_time: **********,
      idp: "local",
      Uid: "************",
      ssbSessionRoleUid: "************",
      OrganisationCode: "YGM86",
      "************:YGM86:NhsJobRole:S8001:G8002:R8008":
        "B0264,B0401,B0468,B0030",
      "************:YGM86:NhsJobRole:S8000:G8000:R8003":
        "B0422,B0107,B0468,B0401,B0278,B0420",
      "************:YGM86:NhsJobRole:S8000:G8000:R8001":
        "B0825,B0107,B0468,B0420,B0401,B0278",
      "************:YGM86:NhsJobRole:S8000:G8000:R8000":
        "B0068,B0401,B0067,B0440,B0442,B0168,B0264,B0441,B0420,B0425,B0468,B0426,B0107,B0277,B0278",
      "************:YGM86:NhsJobRole:S0080:G0440:R5090":
        "B0825,B0002,B0274,B1300,B0267,B0830,B0272,B0835",
      "************:YGM86:NhsJobRole:S0080:G0440:R5110":
        "B0830,B0825,B8010,B0820,B0054,B8011,B0143,B0330,B0835,B8013,B0815",
      scope: ["api1"],
      amr: ["custom"]
    } as any) as IJwtClaims;

    expect(
      permissionService.doesJwtHaveClaimActivityCode(claims, "B0401")
    ).toBe(true);

    expect(
      permissionService.doesJwtHaveClaimActivityCode(claims, "B8011")
    ).toBe(true);

    expect(
      permissionService.doesJwtHaveClaimActivityCode(claims, "B9999")
    ).toBe(false);
  });

  it("doesJwtHaveClaimRole", () => {
    const claims = ({
      nbf: **********,
      exp: **********,
      iss: "https://identityserver.sta.apps.ic24.nhs.uk:778",
      aud: [
        "https://identityserver.sta.apps.ic24.nhs.uk:778/resources",
        "api1"
      ],
      client_id: "ro.client",
      sub: "38d77199-c529-42df-b7f0-ebed0d831329",
      auth_time: **********,
      idp: "local",
      Uid: "************",
      ssbSessionRoleUid: "************",
      OrganisationCode: "YGM86",
      "************:YGM86:NhsJobRole:S8001:G8002:R8008":
        "B0264,B0401,B0468,B0030",
      "************:YGM86:NhsJobRole:S8000:G8000:R8003":
        "B0422,B0107,B0468,B0401,B0278,B0420",
      "************:YGM86:NhsJobRole:S8000:G8000:R8001":
        "B0825,B0107,B0468,B0420,B0401,B0278",
      "************:YGM86:NhsJobRole:S8000:G8000:R8000":
        "B0068,B0401,B0067,B0440,B0442,B0168,B0264,B0441,B0420,B0425,B0468,B0426,B0107,B0277,B0278",
      "************:YGM86:NhsJobRole:S0080:G0440:R5090":
        "B0825,B0002,B0274,B1300,B0267,B0830,B0272,B0835",
      "************:YGM86:NhsJobRole:S0080:G0440:R5110":
        "B0830,B0825,B8010,B0820,B0054,B8011,B0143,B0330,B0835,B8013,B0815",
      scope: ["api1"],
      amr: ["custom"]
    } as any) as IJwtClaims;

    expect(permissionService.doesJwtHaveClaimRole(claims, "G8002")).toBe(true);

    expect(permissionService.doesJwtHaveClaimRole(claims, "G9999")).toBe(false);
  });

  it("B0107", () => {
    const claims = ({
      nbf: **********,
      exp: **********,
      iss: "https://identityserver.prd.apps.ic24.nhs.uk",
      aud: ["https://identityserver.prd.apps.ic24.nhs.uk/resources", "api1"],
      client_id: "ro.client",
      sub: "38d77199-c529-42df-b7f0-ebed0d831329",
      auth_time: **********,
      idp: "local",
      OrganisationCode: [
        "F81043",
        "AH5",
        "07H",
        "Y00082",
        "K83624",
        "Y02804",
        "Y00234",
        "99E",
        "NVE01",
        "NNJ"
      ],
      "F81043:NhsJobRole:S8000:G8000:R8000":
        "B0540,B0015,B0530,B0371,B0435,B0252,B0304,B0302,B0550,B0468,B0462,B0492,B0264,B0250,B0020,B1681,B0016,B0082,B1065,B1101,B0445,B0370,B0018,B0372,B0555,B1611,B0312,B0730,B0380,B0382,B0253,B0310,B0500,B0390,B0011",
      "AH5:NhsJobRole:S8000:G8000:R8000":
        "B0390,B1681,B1842,B0371,B0422,B0420,B0011,B1844,B0435,B0304,B0168,B1101,B0302,B0530,B0252,B0492,B0250,B1611,B0380,B0466,B0312,B0540,B0370,B0550,B0372,B0310,B0401,B0730,B1845,B0360,B0468,B0020,B1843,B0825,B0462,B1110,B0082,B0253,B0445,B0500,B0382",
      "07H:NhsJobRole:S8000:G8000:R8000":
        "B0445,B0302,B0390,B1120,B0310,B0382,B1103,B1101,B0730,B0372,B1110,B1115,B0034,B1130,B0360,B0254,B0312,B0380,B0040,B0435",
      "Y00082:NhsJobRole:S0010:G0020:R0260":
        "B0360,B0264,B1611,B0445,B0020,B0254,B0380,B0312,B0420,B0382,B0730,B0340,B0435,B0462,B0304,B0372,B0082,B1681,B0466,B0371,B0302,B0492",
      "Y00082:NhsJobRole:S8001:G8002:R8008":
        "B0360,B0264,B1611,B0445,B0020,B0254,B0380,B0312,B0420,B0382,B0730,B0340,B0435,B0462,B0304,B0372,B0082,B1681,B0466,B0371,B0302,B0492",
      "K83624:NhsJobRole:S0010:G0020:R0260":
        "B0360,B0435,B0420,B1681,B0380,B0082,B0466,B1611,B0304,B0371,B0492,B0462,B0372,B0302,B0445,B0382,B0312,B0264,B0020,B0340,B0254,B0730",
      "Y02804:NhsJobRole:S8001:G8002:R8008":
        "B0360,B0435,B0420,B1681,B0380,B0082,B0466,B1611,B0304,B0371,B0492,B0462,B0372,B0302,B0445,B0382,B0312,B0264,B0020,B0340,B0254,B0730",
      "Y02804:NhsJobRole:S0010:G0020:R0260":
        "B0445,B1611,B0254,B0730,B0302,B1681,B0340,B0312,B0435,B0264,B0020,B0360,B0380,B0462,B0304,B0382,B0492,B0082,B0371,B0372,B0466,B0420",
      "Y00234:NhsJobRole:S0010:G0020:R0260":
        "B0435,B0462,B0445,B0492,B0082,B0020,B0312,B0304,B0360,B0730,B0302,B0420,B1681,B0372,B0371,B0264,B0380,B0466,B0254,B0382,B1611,B0340",
      "Y00234:NhsJobRole:S8001:G8002:R8008":
        "B0435,B0462,B0445,B0492,B0082,B0020,B0312,B0304,B0360,B0730,B0302,B0420,B1681,B0372,B0371,B0264,B0380,B0466,B0254,B0382,B1611,B0340",
      "99E:NhsJobRole:S8000:G8000:R8000":
        "B0020,B0011,B0253,B0390,B0082,B0466,B0530,B0278,B0312,B0382,B0304,B0380,B1101,B0168,B0302,B0420,B0370,B0422,B1611,B0500,B0372,B1103,B0435,B0730,B0360,B0264,B0085,B1681,B0825,B0445,B0540,B0257,B0401,B0250,B0107,B0492,B0371,B0462,B0468,B0550,B0252",
      "NVE01:NhsJobRole:S8000:G8000:R8000":
        "B0825,B0085,B0370,B0264,B0420,B0257,B0380,B0468,B0401,B0107,B0030,B0278,B0572",
      "NNJ:NhsJobRole:S8000:G8000:R8000":
        "B1101,B0340,B0082,B0468,B0372,B0312,B0435,B0360,B0098,B0370,B0445,B0020,B1611,B0371,B0382,B0390,B0254,B0420,B0730,B0011,B0253,B0380,B0466",
      "NNJ:NhsJobRole:S0010:G0020:R0260":
        "B0371,B0462,B0730,B0445,B0340,B1611,B0372,B0302,B0264,B0435,B0312,B0020,B0492,B0420,B0082,B0380,B0382,B0466,B0360,B1681,B0254,B0304",
      scope: ["api1"],
      amr: ["custom"]
    } as any) as IJwtClaims;

    expect(
      permissionService.doesJwtHaveClaimActivityCode(claims, "B0107")
    ).toBe(true);
  });
});

describe("PermissionService Live Test", () => {
  it("test perms", () => {
    const data: Record<
      string,
      ICleoPermission
    > = (mockLiveOutput as any) as Record<string, ICleoPermission>;

    let res = new PermissionService().simpleKeyPerms(data);

    expect(res["MAKE APPOINTMENT"].PermissionAction).toBe("MAKE APPOINTMENT");

    res = new PermissionService().simpleKeyPerms({
      "Call.RESET_SERVICE": ({
        PermissionActionMode: "",
        PermissionFormulaResult: "1"
      } as any) as ICleoPermission
    });
    expect(res["RESET_SERVICE"].PermissionAction).toBe("RESET_SERVICE");
  });
});
