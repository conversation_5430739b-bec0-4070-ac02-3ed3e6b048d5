<template>
  <div :class="isTabBodyDisabled ? 'adapter--element-disabled' : ''">
    <div class="paccs-condition-tab-body--header">
      <LoadingSpinner v-if="conditionTab.isLoading"></LoadingSpinner>
      <div class="paccs-condition-tab-body--header-buttons">
        <PaccsButtonPwJump
          title="Emergency Treatment Centres"
          :id="conditionTab.paccsSymptomModel.etcJump"
          :symptom-group="conditionTab.paccsSymptomModel.symptomGroup"
          button-type="ET"
          :paccs-symptom-model-template-id="
            conditionTab.paccsSymptomModel.templateId
          "
          class="adapter-button adapter-button--red"
        ></PaccsButtonPwJump>

        <div class="adapter-button--separator"></div>

        <PaccsButtonPwJump
          title="Service Search"
          :id="conditionTab.paccsSymptomModel.servicesJump"
          :symptom-group="conditionTab.paccsSymptomModel.symptomGroup"
          button-type="SS"
          :paccs-symptom-model-template-id="
            conditionTab.paccsSymptomModel.templateId
          "
          class="adapter-button adapter-button--orange"
        ></PaccsButtonPwJump>
      </div>
    </div>

    <div>
      <div
        class="paccs-condition-tab-body--header-label paccs-condition-tab-body--header-label-summary"
      >
        Conditions/Symptoms to Consider
      </div>
      <div
        class="paccs-condition-tab-body--header-label paccs-condition-tab-body--header-label-checkbox-type"
      >
        Considered
      </div>
      <div
        class="paccs-condition-tab-body--header-label paccs-condition-tab-body--header-label-checkbox-type"
      >
        Suspected
      </div>
    </div>

    <div
      v-for="paccsConditionOrder in conditionTab.paccsConditionOrder"
      :key="paccsConditionOrder.condition.conditionId"
    >
      <PaccsCondition
        class="paccs-condition-tab-body--paacs-condition"
        :class="getPaccsConditionCss(paccsConditionOrder.condition)"
        :paccs-symptom-model="conditionTab.paccsSymptomModel"
        :paccs-condition="paccsConditionOrder.condition"
        :triage-record-condition-hydration="
          getTriageRecordConditionHydration(
            paccsConditionOrder.condition.conditionId
          )
        "
        :order-number="paccsConditionOrder.orderNo"
      ></PaccsCondition>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  onBeforeMount,
  PropType,
  SetupContext,
  watch
} from "@vue/composition-api";
import { loggerInstance } from "@/common/Logger";
import { IConditionTab } from "@/paccs/paccs-symptom-tabs/paccs-symptom-tabs-models";
import { PaccsService } from "@/paccs/paccs-service";
import { PaccsSymptomService } from "@/paccs/paccs-symptom-search/paccs-symptom-service";
import { CommonService } from "@/common/common-service";
import LoadingSpinner from "@/common/ui/loading-spinner.vue";
import {
  IPathwaysDatasetLegacy,
  ITriageRecordCondition
} from "@/paccs/paccs-models";
import PaccsButtonPwJump from "@/paccs/buttons/paccs-button-pw-jump.vue";

import PaccsCondition from "@/paccs/paccs-condition-tab-body/paccs-condition/paccs-condition.vue";
import {
  IPaccsCondition,
  IPaccsConditionOrder
} from "@/paccs/paccs-condition-tab-body/paccs-condition-models";
import {
  IPaccsStoreState,
  PACCS_STORE_CONST,
  paccsStore
} from "@/paccs/paccs-store";
import { appStore } from "@/store/store";
import { IPathwaysJumpToPayload } from "@/paccs/pathways/pathways-models";

const paccsService: PaccsService = new PaccsService();
const paccsSymptomService: PaccsSymptomService = new PaccsSymptomService();
const commonService: CommonService = new CommonService();

export default defineComponent({
  name: "paccs-condition-tab-body",
  props: {
    conditionTab: {
      type: Object as PropType<IConditionTab>,
      default: () => {
        return paccsService.factoryConditionTab(
          paccsSymptomService.factoryPaccsSymptomModel()
        );
      }
    },
    triageRecordConditionHydration: {
      type: Array as PropType<ITriageRecordCondition[]>,
      default: () => {
        return [];
      }
    },
    isTabBodyDisabled: {
      type: Boolean,
      default: () => {
        return false;
      }
    }
  },
  components: {
    LoadingSpinner,
    PaccsCondition,
    PaccsButtonPwJump
  },
  setup(
    props: {
      conditionTab: IConditionTab;
      triageRecordConditionHydration: ITriageRecordCondition[];
      isTabBodyDisabled: boolean;
    },
    context: SetupContext
  ) {
    const store = appStore;

    const paccsStoreState = computed<IPaccsStoreState>(() => {
      return store.state[PACCS_STORE_CONST.PACCS__CONST_MODULE_NAME];
    });

    watch(
      () => props.conditionTab,
      (newValue: IConditionTab) => {
        loggerInstance.log("paccs-condition-tab-body: IConditionTab changed");
      }
    );

    const mapTriageRecordConditionHydration = computed<
      Record<string, ITriageRecordCondition>
    >(() => {
      return commonService.convertArrayToObject(
        "quId",
        props.triageRecordConditionHydration
      );
    });

    function getPaccsConditionCss(paccsCondition: IPaccsCondition): string {
      const map: Record<number, string> = {
        1: "paccs-condition-tab-body--paacs-condition-urgent",
        2: "paccs-condition-tab-body--paacs-condition-medium",
        3: "paccs-condition-tab-body--paacs-condition-mild"
      };
      return map[paccsCondition.importance]
        ? map[paccsCondition.importance]
        : "";
    }

    function getTriageRecordConditionHydration(
      conditionId: string
    ): ITriageRecordCondition {
      return mapTriageRecordConditionHydration.value[conditionId]
        ? mapTriageRecordConditionHydration.value[conditionId]
        : paccsService.factoryTriageRecordCondition(
            paccsStoreState.value.addCaseRecordResponse.caseId,
            paccsStoreState.value.pathwaysDataset,
            props.conditionTab.paccsSymptomModel
          );
    }

    return {
      getPaccsConditionCss,
      mapTriageRecordConditionHydration,
      getTriageRecordConditionHydration
    };
  }
});
</script>

<style>
.paccs-condition-tab-body--header {
  height: 3em;
  padding: 1em;
}

.paccs-condition-tab-body--header-buttons {
  float: right;
}

.paccs-condition-tab-body--header-label {
  display: inline-block;
  font-weight: 600;
  padding: 0 1em 1em 1em;
}

.paccs-condition-tab-body--header-label-summary {
  width: 40%;
}

.paccs-condition-tab-body--header-label-checkbox-type {
  width: 10%;
}

.paccs-condition-tab-body--paacs-condition {
  margin-bottom: 0.25em;
  padding: 1em;
  border: 1px solid lightgrey;
}

.paccs-condition-tab-body--paacs-condition-urgent {
  border-left: 0.5em solid red;
}

.paccs-condition-tab-body--paacs-condition-medium {
  border-left: 0.5em solid yellow;
}

.paccs-condition-tab-body--paacs-condition-mild {
  border-left: 0.5em solid green;
}
</style>
