import {
  ICompleteControllerInput,
  useCompleteController
} from "../../../useCompleteController";
import { patientReferredToFurtherActionButtonOption } from "@/calls/details/complete/components/patient-referred-to/patient-referred-to-models";
import { ICallDetail } from "@/calls/details/call-details-models";
import {
  factoryCallDetail,
  factoryICallDetailState
} from "@/calls/details/call-detail-service";
import {
  CompleteStepName,
  IStep
} from "@/calls/details/complete/complete-models";

// const callDetail: ICallDetail = factoryCallDetail();

describe("CAS Clini useCompleteController", () => {
  it("CAS Clini contact made flow", () => {
    const callDetailFcms: ICallDetail = factoryCallDetail();

    callDetailFcms.Service = {
      id: 14,
      serviceType: "111",
      name: "FCMS"
    };

    const completeControllerInput: ICompleteControllerInput = {
      completeProcess: "COMPLETE_PROCESS",
      callDetail: callDetailFcms,
      callDetailState: factoryICallDetailState(),
      userRole: "[CAS CLINI]",
      userPermissions: {
        COMPLETE_USE_ROTA_CLINICIAN: {
          PermissionAccess: "A",
          PermissionForm: "CALL",
          PermissionAction: "COMPLETE_USE_ROTA_CLINICIAN"
        }
      },
      dx: {
        requiringValidation: []
      },
      failedContactConfig: {
        config: {
          attemptsRequired: 2,
          minsInterval: 20
        }
      },
      userRoleTypes: {
        isCasClinician: true,
        isClinician: true
      }
    };

    // const step: IStep<CompleteStepName>;

    const controller = useCompleteController(completeControllerInput);
    controller.state.debug = false;

    expect(controller.state.autoProgress).toBe(true);

    expect(controller.state.currentStep).toBe("END_ASSESSMENT_CONFIRMATION");

    controller.onEndAssessmentConfirmation();
    expect(controller.state.currentStep).toBe("HOW_WAS_CASE_MANAGED");

    controller.onHowMangedSelected({
      id: "BASE",
      description: "Base - Face to Face",
      value: "3-BaseF2F"
    });

    expect(controller.state.currentStep).toBe("CONTACT_MADE");
    controller.onContactMade({
      id: "CONTACT_MADE",
      description: "Contact Made",
      value: "CONTACT_MADE"
    });

    expect(controller.state.currentStep).toBe("PATIENT_REFERRED_TO");
    controller.onPatientReferredTo({
      referredTo: {
        id: "17-Other Secondary Care",
        description: "Other Secondary Care",
        value: "17-Other Secondary Care"
      },
      referredText: "Some text",
      furtherActionGP: { id: "", description: "", value: "" },
      furtherActionGPText: ""
    });
    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("READ_CODES");
    expect(controller.state.validationMessages.length).toBe(0);
    controller.onReadCodesSelected([
      {
        ReadCode: "123",
        ReadCodeDescription: "Some description",
        HasChildren: false
      }
    ]);

    controller.goto("NEXT");
    expect(controller.state.currentStep).toBe("TAXI");
    controller.goto("NEXT");
    expect(controller.state.validationMessages.length).toBe(1);

    controller.onTaxiSelected({
      id: "YES",
      description: "Yes",
      value: "YES"
    });

    expect(controller.state.currentStep).toBe("VULNERABILITY");
    expect(controller.state.isProcessComplete).toBe(false);

    controller.onVulnerabilitySelected({
      adult: {
        id: "YES",
        description: "Yes",
        value: "YES"
      },
      child: {
        id: "YES",
        description: "Yes",
        value: "YES"
      },
      mcaAssessed: {
        id: "NO",
        description: "No",
        value: "NO"
      },
      mcaRequired: {
        id: "NO",
        description: "No",
        value: "NO"
      }
    });
    controller.goto("NEXT");
    expect(controller.state.isProcessComplete).toBe(true);
  });
});
