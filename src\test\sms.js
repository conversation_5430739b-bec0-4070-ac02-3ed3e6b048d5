function smsDialog() {
  var prefix = "sms-dialog--";
  var state = {
    input: {
      phoneNumber: "",
      message: ""
    },
    data: {
      callNo: "",
      dialogReference: null,
      config: {
        enabled: false,
        textCharsPerPage: 160,
        textMaxChars: 1600,
        sendMessageUser: ""
      },
      sendSuccessful: false
    }
  };

  var smsApi = cleoSmsApi();

  function init() {
    if (CallControllerClient.areWeInCallForm()) {
      state.data.callNo = CallControllerClient.m_call_id;
      state.input.phoneNumber = CallControllerClient.getFieldValue(
        "CallTelNo_R"
      );
    } else {
      if (CGC.clickItem.CallNo && CGC.clickItem.CallNo) {
        state.data.callNo = CGC.clickItem.CallNo;
      } else {
        return CallControllerClient.raiseWarningDialog(
          "Error",
          "Failed to get a Call Number"
        );
      }
      if (CGC.clickItem && CGC.clickItem.CallTelNo_R) {
        state.input.phoneNumber = CGC.clickItem.CallTelNo_R;
      }
    }
  }

  function openDialog() {
    init();

    CallControllerClient.showBusyMask(true);

    // Get client configuration for SMS
    smsApi.getClientConfig().then(function(response) {
      if (response.RESULT === "SUCCESS") {
        state.data.config = response.DATA;

        // Create and show the dialog
        var dialogHtml = createDialogHtml();
        state.data.dialogReference = $(dialogHtml).dialog({
          title: "Send a Manual SMS",
          modal: true,
          width: 600,
          height: 350,
          resizable: false,
          close: function() {
            removeClickHandlers();
            $(this)
              .dialog("destroy")
              .remove();
          }
        });

        // Populate the phone number field with the value from state
        $("#" + prefix + "phone-number").val(state.input.phoneNumber);

        addClickHandlers();
      } else {
        alert("Error loading SMS configuration: " + response.MESSAGE);
      }
      CallControllerClient.showBusyMask(false);
    });
  }

  function createDialogHtml() {
    var html = [];
    html.push("<div id='" + prefix + "container'>");

    html.push("<div class='ic24-flex-column ic24-gap--standard'>");

    // Phone number input row
    html.push(
      "<div class='ic24-flex-row ic24-justify-flex-space-between' style='margin-top: 8px;'>"
    );

    html.push(
      "<div class='ic24-flex-row ic24-gap--standard ic24-justify-flex-row-vert-center'>"
    );
    html.push("<label class='adapter-form-label'>Send To:</label>");
    html.push(
      "<input type='text' id='" +
        prefix +
        "phone-number' class='adapter-form-input' placeholder='Enter phone number...' />"
    );
    html.push("</div>");

    html.push(
      "<div class='ic24-flex-row ic24-gap--standard ic24-justify-flex-row-vert-center'>"
    );
    html.push("<label class='adapter-form-label'>Send From:</label>");
    html.push(
      "<input type='text' id='" +
        prefix +
        "send-from' class='adapter-form-input' placeholder='Enter phone number...' value='" +
        state.data.config.sendMessageUser +
        "' disabled/>"
    );
    html.push("</div>");

    html.push("</div>");

    // Grey line after header
    html.push("<div style='height: 1px;background: lightgray;'></div>");

    // Message text area - now with full width and resize disabled
    html.push("<label class='adapter-form-label'>Enter your text:</label>");
    html.push(
      "<textarea id='" +
        prefix +
        "message' class='adapter-form-textarea ic24-full-width' style='resize: none;' rows='6' placeholder='Type here...'></textarea>"
    );

    html.push(
      "<div class='ic24-flex-row ic24-gap--standard ic24-justify-flex-end'>"
    );

    // Character counter
    html.push(
      "<div class='ic24-flex-row ic24-gap--standard ic24-justify-flex-row-vert-center'>"
    );
    html.push("<span>Total Characters: </span>");
    html.push("<span id='" + prefix + "char-count'>0</span>");
    html.push(" / " + state.data.config.textMaxChars);
    html.push("</div>");

    // Page counter
    html.push(
      "<div class='ic24-flex-row ic24-gap--standard ic24-justify-flex-row-vert-center'>"
    );
    html.push("<span>Total text messages: </span>");
    html.push("<span id='" + prefix + "page-count'>0</span>");
    html.push("</div>");

    html.push("</div>");

    // Error message area
    html.push(
      "<div id='" + prefix + "message-error' class='adapter-form-error'></div>"
    );

    // Buttons
    html.push(
      "<div class='adapter-form-row' style='margin-top: 20px; display: flex; justify-content: space-between;'>"
    );
    html.push("<button id='" + prefix + "cancel'>Exit</button>");
    html.push("<button id='" + prefix + "submit'>Send SMS</button>");
    html.push("</div>");

    html.push("</div>"); //  flex-column

    html.push("</div>"); // container
    return html.join("");
  }

  function addClickHandlers() {
    // Phone number input handler
    $("#" + prefix + "phone-number").on("change", function() {
      state.input.phoneNumber = $(this).val();
    });

    // Message text area handler with character counting
    $("#" + prefix + "message").on("input", function() {
      state.input.message = $(this).val();
      updateCounters();
    });

    // Cancel button handler
    $("#" + prefix + "cancel").on("click", function() {
      cancel();
    });

    // Submit button handler
    $("#" + prefix + "submit").on("click", function() {
      submit();
    });
  }

  function updateCounters() {
    var charCount = state.input.message.length;
    var pageCount =
      Math.ceil(charCount / state.data.config.textCharsPerPage) || 0;

    var jCharCount = $("#" + prefix + "char-count");
    var jPageCount = $("#" + prefix + "page-count");
    var jMessageError = $("#" + prefix + "message-error");

    jCharCount.text(charCount);
    jPageCount.text(pageCount);

    jCharCount.css("color", "");
    jPageCount.css("color", "");
    jMessageError.css("color", "");

    // if the message size exceeds 1 page, write this message:
    // "Warning: You have exceeded the maximum number of characters limit for a single message, multiple SMS messages will be sent.";
    if (pageCount > 1) {
      writeError(
        "Warning: You have exceeded the maximum number of characters limit for a single message, multiple SMS messages will be sent."
      );

      // style the message red
      jMessageError.css("color", "red");

      // make page count red
      jPageCount.css("color", "red");
    } else {
      writeError("");
    }

    // Highlight in red if over max chars
    if (charCount > state.data.config.textMaxChars) {
      // write message indicating the message is too long
      writeError(
        "Message exceeds maximum character limit of " +
          state.data.config.textMaxChars +
          "."
      );

      jCharCount.css("color", "red");
    }
  }

  function removeClickHandlers() {
    $("#" + prefix + "phone-number").off("change");
    $("#" + prefix + "message").off("input");
    $("#" + prefix + "cancel").off("click");
    $("#" + prefix + "submit").off("click");
  }

  // write a function isMobileNumber
  function isMobileNumber(phoneNumber) {
    phoneNumber = phoneNumber.replace(/ /g, "");
    const pattern = /^(\+44\s?7\d{3}|\(?07\d{3}\)?)\s?\d{3}\s?\d{3}$/;
    return pattern.test(phoneNumber);
  }

  function validate() {
    // Check if phone number is provided
    if (state.input.phoneNumber === "") {
      writeError("Please enter a phone number.");
      return false;
    }

    // Check if phone number is valid UK mobile number
    if (!isMobileNumber(state.input.phoneNumber)) {
      writeError("Please enter a valid UK mobile number.");
      return false;
    }

    // Check if message is provided
    if (state.input.message === "") {
      writeError("Please enter a message.");
      return false;
    }

    // Check if message is too long
    if (state.input.message.length > state.data.config.textMaxChars) {
      writeError(
        "Message exceeds maximum character limit of " +
          state.data.config.textMaxChars +
          "."
      );
      return false;
    }

    return true;
  }

  function cancel() {
    // Check if user has entered data that would be lost
    if (
      (state.input.phoneNumber !== "" || state.input.message !== "") &&
      !state.data.sendSuccessful
    ) {
      var dialogHtml = [];
      dialogHtml.push("<div style='width: 100%;height: 100%;'>");
      dialogHtml.push("<strong>You will lose your changes.</strong>");

      // a light grey line
      dialogHtml.push(
        "<div style='height: 1px;background: lightgray;margin-top: 4px;margin-bottom: 4px;'></div>"
      );

      dialogHtml.push(
        "You are about to exit this manual SMS and changes will not be lost."
      );
      dialogHtml.push("Are you sure you want to exit manual SMS?");
      dialogHtml.push("</div>");

      // Create confirmation dialog
      $("<div>")
        .attr("title", "Confirm Exit")
        .html(dialogHtml.join(""))
        .dialog({
          modal: true,
          width: 400,
          height: 200,
          resizable: false,
          buttons: {
            "Yes (Exit)": function() {
              $(this).dialog("close");
              state.data.dialogReference.dialog("close");
            },
            "No (Continue Editing)": function() {
              $(this).dialog("close");
            }
          },
          create: function() {
            $(this)
              .parent()
              .find(".ui-dialog-buttonset button:first")
              .css({ position: "absolute", left: "4px" });
            $(this)
              .parent()
              .find(".ui-dialog-buttonset button:last")
              .css("float", "right");
          }
        });
    } else {
      // No data to lose, just close
      state.data.dialogReference.dialog("close");
    }
  }

  function submit() {
    if (!validate()) {
      return;
    }

    // Disable buttons during submission
    $("#" + prefix + "submit").prop("disabled", true);
    $("#" + prefix + "cancel").prop("disabled", true);

    CallControllerClient.showBusyMask(true);

    // Call API to send SMS
    var prom = smsApi.sendSms(state);

    prom.then(function(response) {
      if (response.RESULT === "SUCCESS") {
        //  response paylaod returned in following structure.
        /*
        {
  "RESULT": "SUCCESS",
  "MESSAGE": "",
  "DATA": {
    "success": false,
    "message": "Invalid mobile number format: 02222 222222. Must start with 07, or 447.",
    "errorType": "Invalid mobile number",
    "statusCode": 422,
    "responses": []
  }
}
         */

        if (response.DATA.success) {
          state.data.sendSuccessful = true;
          writeError("SMS sent successfully.");
        } else {
          writeError("SMS failed to send: " + response.DATA.message);
          $("#" + prefix + "submit").prop("disabled", false);
        }

        if (!CallControllerClient.areWeInCallForm()) {
          autoRefreshView();
        }

        // writeError("SMS sent successfully.");
        // setTimeout(function() {
        //   cancel();
        // }, 1500);
      } else {
        writeError("Failed to send SMS: " + response.MESSAGE);
        $("#" + prefix + "submit").prop("disabled", false);
      }
    });

    //  Because HCL IDE...suuuuuccckkkks.
    prom["catch"](function(error) {
      // handle error
      alert("ERROR Oversight validation: " + error + "");
      $("#" + prefix + "submit").prop("disabled", false);
    });

    prom["finally"](function() {
      CallControllerClient.showBusyMask(false);
      $("#" + prefix + "cancel").prop("disabled", false);
    });
  }

  function writeError(message) {
    $("#" + prefix + "message-error").html(message);
  }

  return {
    openDialog: openDialog
  };
}
