import { RouteConfig } from "vue-router";

export const GRID_ROUTES_PATHS = {
  GRID_ROUTE_UNDEFINED: "grid-route-undefined",
  GRID_CATCH_ALL: "catchall",
  GRID_111: "111",
  GRID_FCMS: "fcms",
  GRID_PCAS: "pcas",
  GRID_BASE: "base",
  GRID_TEST: "test"
};

// import Grid111 from "@/calls/grids/grids-named/Grid111.vue";

export default [
  {
    path: "",
    name: GRID_ROUTES_PATHS.GRID_ROUTE_UNDEFINED,
    component: {
      template: "<div>Hello eunxef</div>"
    }
  },
  {
    path: "111",
    name: "111",
    component: {
      template: "<div>Hello 111</div>"
    }
  },
  {
    path: "" + GRID_ROUTES_PATHS.GRID_FCMS,
    name: GRID_ROUTES_PATHS.GRID_FCMS,
    component: () =>
      import(
        /* webpackChunkName: "grid-fcms" */ "@/calls/grids/grids-named/GridFcms.vue"
      )
  },
  {
    path: "" + GRID_ROUTES_PATHS.GRID_PCAS,
    name: GRID_ROUTES_PATHS.GRID_PCAS,
    component: () =>
      import(
        /* webpackChunkName: "grid-pcas" */ "@/calls/grids/grids-named/GridPcas.vue"
      )
  },
  {
    path: "" + GRID_ROUTES_PATHS.GRID_BASE + "/:id",
    name: GRID_ROUTES_PATHS.GRID_BASE,
    component: () =>
      import(
        /* webpackChunkName: "grid-pcas" */ "@/calls/grids/grids-named/GridPcas.vue"
      )
  },
  {
    path: "multi-test",
    name: "multi-test",
    component: () =>
      import(
        /* webpackChunkName: "grid-multi-test" */ "@/calls/grids/grids-named/GridMultiTest.vue"
      )
  }
];
