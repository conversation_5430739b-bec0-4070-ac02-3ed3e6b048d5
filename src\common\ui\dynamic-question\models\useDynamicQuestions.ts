/**
 * Dynamic Questions Management System
 *
 * This module provides a comprehensive system for managing dynamic, conditional question flows
 * in Vue.js applications. It handles question visibility based on both flow progression and
 * conditional logic, supports backward navigation with proper state cleanup, and provides
 * validation for mandatory questions.
 *
 * Key Features:
 * - Flow-based question progression with backward navigation support
 * - Conditional question visibility based on answers to other questions
 * - Automatic state cleanup when questions become hidden
 * - Validation of mandatory questions
 * - Support for complex question types (radio, checkbox, select, text)
 *
 * Usage:
 * ```typescript
 * const { init, onQuestionAnswered, goToQuestion, getOutput } = useDynamicQuestions();
 *
 * // Initialize with questions
 * init(questions);
 *
 * // Handle user answers
 * onQuestionAnswered(question);
 *
 * // Navigate backward
 * goToQuestion('previousQuestionId');
 *
 * // Get current state
 * const output = getOutput();
 * ```
 *
 * ## Question Array Formats
 *
 * The system supports two different formats for defining question arrays, each suited to
 * different use cases and data sources:
 *
 * ### 1. Legacy JSON String Format (Server-driven)
 * Used when questions come from server APIs or legacy systems as serialized JSON strings.
 *
 * **Example from `mockAuditQuestions.ts`:**
 * ```typescript
 * export const mockAuditQuestions: LegacyKeywordServerResponse = {
 *   AUDIT_QUESTION_DATA_CLASS_VISIT: {
 *     codeID1: '[\r\n  {\r\n    "id": "verificationOfDeath",\r\n    "type": "radio",\r\n    "label": "Q1 - Was this case for verification of death?",\r\n    "options": ["Yes", "No"],\r\n    "mandatory": true,\r\n    "value": null,\r\n    "visible": true\r\n  },\r\n  {\r\n    "id": "safeguardingReferral",\r\n    "type": "text",\r\n    "label": "Please enter your GMC# to support the onward Practice process",\r\n    "mandatory": true,\r\n    "value": null,\r\n    "visible": false,\r\n    "condition": "answers[\u0027verificationOfDeath\u0027] === \u0027Yes\u0027"\r\n  }\r\n]'
 *   }
 * };
 * ```
 *
 * **Characteristics:**
 * - Questions stored as JSON strings in `codeID1` fields
 * - Conditions defined as string expressions (e.g., `"answers['field'] === 'value'"`)
 * - `nextQuestion` logic as string expressions
 * - Requires parsing at runtime using `new Function()`
 * - Suitable for server-driven, configurable question sets
 * - Allows dynamic question configuration without code changes
 *
 * ### 2. Direct TypeScript Object Format (Code-driven)
 * Used when questions are defined directly in TypeScript with function-based logic.
 *
 * **Example from `mockNonCliniPreAssessment.ts`:**
 * ```typescript
 * export const mockNonCliniAssessment: Question[] = [
 *   {
 *     id: "withPatient",
 *     type: "radio",
 *     label: "Q1: Are you with the patient?",
 *     options: ["Yes", "No"],
 *     mandatory: true,
 *     value: null,
 *     visible: true,
 *     nextQuestion: (answer: unknown) =>
 *       answer === "Yes" ? "callerIsClinician" : "canContactPatientDirectly"
 *   },
 *   {
 *     id: "canContactPatientDirectly",
 *     type: "radio",
 *     label: "Q2: Can we contact the patient directly?",
 *     options: ["Yes", "No"],
 *     mandatory: true,
 *     value: null,
 *     visible: false,
 *     condition: "answers['withPatient'] === 'No'",
 *     nextQuestion: (answer: unknown) => "callerIsClinician"
 *   }
 * ];
 * ```
 *
 * **Characteristics:**
 * - Questions defined as direct TypeScript objects
 * - Conditions can be either strings or functions
 * - `nextQuestion` can be strings, functions, or dynamic expressions
 * - Better TypeScript intellisense and type safety
 * - Easier to maintain and debug in development
 * - Suitable for complex, code-controlled question flows
 *
 * ## How Each Format is Processed
 *
 * ### Legacy Format Processing:
 * 1. JSON string parsed using `JSON.parse()`
 * 2. String conditions converted to functions via `new Function()`
 * 3. String `nextQuestion` logic converted to functions
 * 4. Result passed to `useDynamicQuestions.init()`
 *
 * ### Direct Format Processing:
 * 1. Questions used as-is (no parsing needed)
 * 2. Function-based conditions and nextQuestion logic preserved
 * 3. Direct pass-through to `useDynamicQuestions.init()`
 *
 * ## Choosing Between Formats
 *
 * ### Use Legacy Format When:
 * - Questions come from external APIs or databases
 * - Question sets need to be configurable without code changes
 * - Working with legacy systems that provide JSON-serialized data
 * - Need server-side question management and updates
 *
 * ### Use Direct Format When:
 * - Questions are static and defined in code
 * - Need complex conditional logic with functions
 * - Want full TypeScript type safety and intellisense
 * - Prefer code-based maintenance and version control
 * - Need advanced question flow logic
 *
 * ## Migration Between Formats
 *
 * To convert from Legacy to Direct format:
 * 1. Parse the JSON string to get the question objects
 * 2. Convert string conditions to function expressions
 * 3. Convert string nextQuestion logic to functions
 * 4. Move from server response structure to direct Question[] array
 *
 * To convert from Direct to Legacy format:
 * 1. Serialize Question[] to JSON string
 * 2. Convert function conditions to string expressions
 * 3. Convert function nextQuestion to string expressions
 * 4. Wrap in LegacyKeywordServerResponse structure
 */

import {
  DynamicQuestionsOutput,
  DynamicQuestionsState,
  Question
} from "@/common/ui/dynamic-question/models/dynamic-question-models";
import { UnwrapRef } from "@vue/composition-api";
import { deepCloneObject, simpleObjectClone } from "@/common/common-utils";

/**
 * Composable for managing dynamic question flows with conditional visibility and navigation
 *
 * @param stateInject - Optional external state to use instead of creating new state
 * @returns Object containing state and methods for question management
 */
export function useDynamicQuestions(
  stateInject?: UnwrapRef<DynamicQuestionsState>
) {
  // State management - either use injected state or create new reactive state
  const state:
    | DynamicQuestionsState
    | UnwrapRef<DynamicQuestionsState> = stateInject
    ? stateInject
    : {
        questions: [], // Array of all question definitions
        answers: {}, // Map of question IDs to their current answer values
        currentQuestionId: null, // ID of the currently active question
        questionFlow: [], // Ordered array of question IDs that have been reached
        usingNextFunctionality: false
      };

  /**
   * Initialize the dynamic questions system with a set of questions
   *
   * This function sets up the initial state, parses any string-based conditions
   * and nextQuestion logic into executable functions, and starts the question flow
   * with the first question visible.
   *
   * @param questions - Array of question definitions to initialize with
   */
  function init(questions: Question[]) {
    // Deep clone and parse questions to convert string-based logic to functions
    const parsedQuestions: Question[] = deepCloneObject(questions).map(
      question => {
        // Convert string-based conditions to executable functions
        // This allows conditions like "answers['q1'] === 'Yes'" to be evaluated dynamically
        if (question.condition && typeof question.condition === "string") {
          question.condition = new Function(
            "answers",
            `return ${question.condition};`
          ) as any;
        }

        
        // Convert string-based nextQuestion logic to executable functions
        // This allows dynamic question flow based on answers
        if (
          question.nextQuestion &&
          typeof question.nextQuestion === "string"
        ) {
          question.nextQuestion = new Function(
            "answer",
            "answers",
            `return ${question.nextQuestion};`
          ) as any;
        }

        // 
        if (question.nextQuestion) {
          state.usingNextFunctionality = true;
        }

        return question;
      }
    );

    // Store the parsed questions in state
    state.questions = deepCloneObject(parsedQuestions);

    // Initialize the question flow starting with the first question
    if (parsedQuestions.length > 0) {
      state.currentQuestionId = parsedQuestions[0].id;
      state.questionFlow = [parsedQuestions[0].id];
      // Update visibility to ensure proper initial state

      // If not using nextQuestion functionality, update visibility based on conditions
      if (!state.usingNextFunctionality) {
        updateVisibility();
      }
      
    }
  }

  /**
   * Handle when a user answers a question
   *
   * This function updates the answer state, determines the next question in the flow
   * based on the answer and question's nextQuestion logic, and updates visibility
   * of all questions based on the new state.
   *
   * @param question - The question that was answered
   */
  function onQuestionAnswered(question: Question) {
    // Store the answer in the state
    state.answers[question.id] = question.value;

    // Handle dynamic question flow based on the answer
    if (question.nextQuestion) {
      // Evaluate the next question ID based on the answer
      const nextQuestionId =
        typeof question.nextQuestion === "string"
          ? question.nextQuestion // Static next question
          : question.nextQuestion(question.value, state.answers); // Dynamic evaluation

      if (nextQuestionId) {
        // Add the next question to the flow if it's not already there
        // This prevents duplicate entries while maintaining order
        if (!state.questionFlow.includes(nextQuestionId)) {
          state.questionFlow.push(nextQuestionId);
        }
        // Move to the next question...
        state.currentQuestionId = nextQuestionId;

        //  ...and set it's visibility to true
        const questions: Question[] = state.questions.map((questionClone: Question) => {
          if (nextQuestionId === questionClone.id) {
            questionClone.visible = true;
          }
          return questionClone;
        })

        state.questions = questions;

        // Get the index of nextQuestionId, set visibility to false for questions with index greater than nextQuestionId
        const nextIndex = state.questions.findIndex(q => q.id === nextQuestionId);
        state.questions.forEach((q, index) => {
          if (index > nextIndex) {
            q.visible = false;
            // Clean up state for hidden questions
            delete state.answers[q.id];
            if (q.type === "checkbox") {
              q.value = [];
            } else {
              q.value = "";
            }
          }
        });


      }
    } else   {
      // Update visibility of all questions based on new answers and flow
      updateVisibility();
    }


  }

  /**
   * Update question visibility based on conditional logic
   *
   * This function evaluates each question's condition against the current answers
   * and updates the question's visibility accordingly. When a question becomes
   * hidden, its answer is cleared to ensure clean state management.
   */
  function setVisibility() {
    state.questions.forEach(question => {
      // Initialize question value if undefined (defensive programming)
      if (typeof question.value === "undefined") {
        if (question.type === "checkbox") {
          question.value = []; // Array for multiple selections
        } else {
          question.value = ""; // String for single values
        }
      }

      // Evaluate conditional visibility if the question has conditions
      if (question.condition) {
        // Convert string conditions to functions if needed
        if (question.condition && typeof question.condition === "string") {
          question.condition = new Function(
            "answers",
            `return ${question.condition};`
          ) as any;
        }

        // Execute the condition function with current answers
        const condition = question.condition as (
          answers: Record<string, unknown>
        ) => boolean;

        const isVisible = condition(state.answers);

        // Update visibility based on condition evaluation
        question.visible = isVisible;

        // Clean up state when question becomes hidden
        if (!isVisible) {
          // Remove answer from state to prevent stale data
          delete state.answers[question.id];
          // Reset question value for clean slate when it becomes visible again
          if (question.type === "checkbox") {
            question.value = [];
          } else {
            question.value = "";
          }
        }
      }
    });
  }

  /**
   * Update question visibility based on the current question flow
   *
   * This function ensures that only questions that have been reached in the
   * navigation flow are visible. Questions beyond the current point in the
   * flow are hidden and their answers cleared to maintain data integrity
   * during backward navigation.
   */
  function updateVisibilityBasedOnFlow() {
    state.questions.forEach(question => {
      // Determine if question should be visible based on flow progression
      const shouldBeVisible = state.questionFlow.includes(question.id);

      // Only update if visibility state has changed
      if (shouldBeVisible !== question.visible) {
        question.visible = shouldBeVisible;

        // Clean up state for hidden questions
        if (!shouldBeVisible) {
          // Remove answer to prevent stale data
          delete state.answers[question.id];
          // Reset value for clean state when question becomes visible again
          if (question.type === "checkbox") {
            question.value = [];
          } else {
            question.value = "";
          }
        }
      }
    });
  }

  /**
   * Combined visibility update that handles both flow and conditions
   */
  function updateVisibility() {
    // updateVisibilityBasedOnFlow();
    setVisibility();
  }

  /**
   * Validate that all mandatory visible questions have been answered
   *
   * @returns Array of mandatory questions that are visible but unanswered
   */
  function validateAnswers(): Question[] {
    const answers = state.answers;
    const questions = state.questions;

    // Filter to only visible questions
    const visibleQuestions = questions.filter(question => question.visible);

    // Filter to only mandatory questions
    const mandatoryQuestions = visibleQuestions.filter(
      question => question.mandatory
    );

    // Return questions that don't have answers
    return mandatoryQuestions.filter(question => !answers[question.id]);
  }

  /**
   * Navigate to a specific question in the flow (supports backward navigation)
   *
   * This function allows jumping to any previously visited question, truncating
   * the flow to remove all questions after the target question. This ensures
   * that when navigating backward, subsequent questions are hidden and their
   * answers are cleared for data integrity.
   *
   * @param questionId - The ID of the question to navigate to
   */
  function goToQuestion(questionId: string) {
    const questionIndex = state.questionFlow.indexOf(questionId);
    if (questionIndex !== -1) {
      // Truncate the flow to include only questions up to the target
      // This removes all questions that come after the target in the flow
      state.questionFlow = state.questionFlow.slice(0, questionIndex + 1);
      state.currentQuestionId = questionId;

      // Update visibility and clear answers for questions no longer in flow
      updateVisibility();
    }
  }

  /**
   * Navigate to the previous question in the current flow
   *
   * This is a convenience function that moves one step backward in the
   * question flow, useful for implementing "Back" button functionality.
   */
  function goToPreviousQuestion() {
    if (state.currentQuestionId) {
      const currentIndex = state.questionFlow.indexOf(state.currentQuestionId);
      if (currentIndex > 0) {
        const previousQuestionId = state.questionFlow[currentIndex - 1];
        goToQuestion(previousQuestionId);
      }
    }
  }

  /**
   * Get the current state of the dynamic questions system
   *
   * This function returns a snapshot of the current state including validation
   * status and any questions that still need to be answered. The returned
   * object is a clone to prevent external mutation of internal state.
   *
   * @returns Current state with validation information
   */
  function getOutput(): DynamicQuestionsOutput {
    const stateClone = simpleObjectClone(state);
    return {
      ...stateClone,
      isValid: validateAnswers().length === 0, // Valid if no unanswered mandatory questions
      questionsThatNeedAnswer: validateAnswers() // List of questions requiring answers
    };
  }

  // Return the public API of the composable
  return {
    state, // Current internal state (reactive)

    // Core functionality
    init, // Initialize with questions
    onQuestionAnswered, // Handle user answers
    validateAnswers, // Check for unanswered mandatory questions
    getOutput, // Get current state snapshot

    // Navigation
    goToQuestion, // Jump to specific question
    goToPreviousQuestion // Go back one question
  };
}
