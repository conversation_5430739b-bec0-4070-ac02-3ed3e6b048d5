# Grid Filter Persistence Architecture

## Architecture Overview

This document outlines the architectural design for implementing persistent filter states across grid navigation in the CLEO frontend application.

## System Architecture Diagram

```mermaid
graph TB
    A[GridStandard2.vue] --> B[ButtonFilters.vue]
    B --> C[useButtonFiltersController]
    C --> D[useGridFilterState]
    D --> E[Vuex Store Module]
    E --> F[gridFilterStateStore]
    
    G[Grid Navigation] --> H[Filter State Restoration]
    H --> C
    
    I[Filter Changes] --> J[Auto-Save to Store]
    J --> E
    
    K[Reset Filters] --> L[Clear Saved State]
    L --> E
```

## Component Interaction Flow

### 1. Initial Grid Load
```mermaid
sequenceDiagram
    participant GS as GridStandard2
    participant BF as ButtonFilters
    participant BC as useButtonFiltersController
    participant GFS as useGridFilterState
    participant Store as Vuex Store
    
    GS->>BF: Pass gridId + initial filters
    BF->>BC: Initialize with gridId
    BC->>GFS: Check for saved state
    GFS->>Store: getFilterState(gridId)
    Store-->>GFS: Return saved state or null
    GFS-->>BC: Saved state or null
    BC->>BC: Use saved state or defaults
```

### 2. Filter Application
```mermaid
sequenceDiagram
    participant User as User
    participant BF as ButtonFilters
    participant BC as useButtonFiltersController
    participant GFS as useGridFilterState
    participant Store as Vuex Store
    
    User->>BF: Apply filter
    BF->>BC: toggleCleoClientService()
    BC->>BC: Update internal state
    BC->>GFS: saveCurrentState()
    GFS->>Store: setFilterState(gridId, state)
    Store->>Store: Persist in memory
```

### 3. Grid Navigation
```mermaid
sequenceDiagram
    participant User as User
    participant Router as Vue Router
    participant GS as GridStandard2
    participant BC as useButtonFiltersController
    participant GFS as useGridFilterState
    participant Store as Vuex Store
    
    User->>Router: Navigate to different grid
    Router->>GS: Load new grid component
    GS->>BC: Initialize with new gridId
    BC->>GFS: getFilterState(newGridId)
    GFS->>Store: Retrieve saved state
    Store-->>BC: Return saved filters
    BC->>BC: Restore filter state
```

## Data Flow Architecture

### State Management Hierarchy

```
Vuex Root Store
├── Socket Store
├── Config Store
├── Permission Store
├── User Store
├── Context Menu Store
├── Login Store
├── Keywords Store
├── PACCS Store
└── Grid Filter State Store ← NEW
    └── filterStates: Record<SocketGroup, ButtonFiltersControllerState>
        ├── CasCalls: { gridFilterUserInput: {...}, dxGroupTypes: {...} }
        ├── PLS: { gridFilterUserInput: {...}, dxGroupTypes: {...} }
        ├── OverSight: { gridFilterUserInput: {...}, dxGroupTypes: {...} }
        └── ... (other grids)
```

### Filter State Structure

```typescript
interface ButtonFiltersControllerState {
  isLoading: boolean;
  gridFilterUserInput: IGridFilterUserInput;
  dxGroupTypes: Record<DxCodeGroupType, DxCode[]>;
}

interface IGridFilterUserInput {
  QUICK: string;
  COV19: "COV19" | "NOT_COV19" | "";
  TOWN: string;
  CLASSIFICATION: string;
  CLEO_CLIENT_SERVICE: CLEO_CLIENT_SERVICE[];
  DX: GridFilterUserInputDxCodes;
  ASSIGNED_TO: boolean | null;
  BREACHED: boolean | null;
  PDS_TRACED_AND_VERIFIED: boolean | null;
  // ... other filter properties
}
```

## Key Design Decisions

### 1. Vuex Store Module Approach
**Decision**: Use Vuex store module instead of standalone service
**Rationale**: 
- Consistent with existing CLEO architecture
- Centralized state management
- Reactive updates across components
- Better debugging with Vue DevTools

### 2. Memory-Only Persistence
**Decision**: Store filter states in memory only (not localStorage)
**Rationale**:
- Simpler implementation
- No data privacy concerns
- Fresh start on page refresh
- Avoids stale data issues

### 3. Automatic State Saving
**Decision**: Auto-save filter state on every change
**Rationale**:
- Better user experience
- No need to remember to save
- Immediate persistence
- Consistent behavior

### 4. Grid Identifier as Key
**Decision**: Use `SocketGroup` (grid identifier) as the key for storing states
**Rationale**:
- Natural mapping to grid types
- Type-safe with existing enums
- Clear separation of concerns
- Scalable to new grid types

## Performance Considerations

### Memory Usage
- **Impact**: Each grid's filter state is stored in memory
- **Mitigation**: Only store states for grids that have been filtered
- **Monitoring**: Track memory usage in development

### State Cloning
- **Impact**: Deep cloning of filter states for immutability
- **Mitigation**: Use efficient cloning utilities
- **Optimization**: Consider shallow cloning where appropriate

### Reactive Updates
- **Impact**: Vuex reactivity system updates all subscribers
- **Mitigation**: Use computed properties and getters efficiently
- **Optimization**: Minimize unnecessary re-renders

## Security Considerations

### Data Sensitivity
- **Filter Data**: Contains user preferences and potentially sensitive search terms
- **Mitigation**: Memory-only storage, no persistence to disk
- **Compliance**: Aligns with NHS data protection requirements

### User Isolation
- **Concern**: Filter states are per-session, not per-user
- **Current State**: Acceptable as sessions are user-specific
- **Future**: Could extend to user-specific persistence if needed

## Scalability Considerations

### New Grid Types
- **Extensibility**: Adding new `SocketGroup` values automatically supports new grids
- **Configuration**: No additional code needed for new grid types
- **Maintenance**: Centralized filter state management

### Filter Complexity
- **Current**: Supports all existing filter types
- **Future**: Easy to extend `IGridFilterUserInput` interface
- **Backward Compatibility**: Graceful handling of missing properties

## Error Handling Strategy

### Invalid Grid IDs
```typescript
function getFilterState(gridId: SocketGroup): ButtonFiltersControllerState | null {
  if (!gridId || !Object.values(SocketGroupNames).includes(gridId)) {
    console.warn(`Invalid grid ID: ${gridId}`);
    return null;
  }
  return state.filterStates[gridId] || null;
}
```

### Corrupted State
```typescript
function setFilterState(gridId: SocketGroup, filterState: ButtonFiltersControllerState): void {
  try {
    // Validate state structure before saving
    if (!isValidFilterState(filterState)) {
      console.warn(`Invalid filter state for grid ${gridId}`);
      return;
    }
    Vue.set(state.filterStates, gridId, filterState);
  } catch (error) {
    console.error(`Failed to save filter state for grid ${gridId}:`, error);
  }
}
```

### Migration Support
```typescript
function migrateFilterState(oldState: any): ButtonFiltersControllerState {
  // Handle schema changes in filter state structure
  return {
    isLoading: oldState.isLoading || false,
    gridFilterUserInput: migrateGridFilterUserInput(oldState.gridFilterUserInput),
    dxGroupTypes: oldState.dxGroupTypes || getDefaultDxGroupTypes()
  };
}
```

## Testing Strategy

### Unit Tests
- Test store mutations and getters
- Test filter state persistence logic
- Test error handling scenarios

### Integration Tests
- Test filter persistence across grid navigation
- Test multiple concurrent grid states
- Test filter reset functionality

### E2E Tests
- Test complete user workflows
- Test browser refresh scenarios
- Test performance under load

## Monitoring and Debugging

### Development Tools
- Vue DevTools integration for state inspection
- Console logging for state changes
- Performance monitoring for memory usage

### Production Monitoring
- Error tracking for failed state operations
- Performance metrics for filter operations
- User behavior analytics for filter usage

## Future Enhancements

### Session Persistence
- Add localStorage integration for session persistence
- Implement state migration for schema changes
- Add user preferences for persistence behavior

### Advanced Features
- Filter state sharing between users
- Filter templates and presets
- Advanced filter analytics and recommendations

### Performance Optimizations
- Lazy loading of filter states
- State compression for large filter sets
- Debounced state saving for rapid changes

This architecture provides a robust, scalable foundation for persistent grid filter states while maintaining consistency with the existing CLEO frontend architecture.