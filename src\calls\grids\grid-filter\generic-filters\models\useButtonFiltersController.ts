import { reactive } from "@vue/composition-api";
import {
  DxCodeGroupType,
  IGridFilterUserInput
} from "@/calls/grids/grid-filter/grid-filter-models";
import { formatUserDominoName, simpleObjectClone } from "@/common/common-utils";
import { CLEO_CLIENT_SERVICE, DxCode } from "@/common/common-models";
import { factoryButtonFiltersControllerState } from "@/calls/grids/grid-filter/generic-filters/models/generic-filters-service";
import { GridFilterService } from "@/calls/grids/grid-filter/grid-filter-service";
import { useUserStore } from "@/user/useUserStore";
import { SocketGroup } from "@/calls/grids/grid-models";
import { useGridFilterState } from "@/calls/grids/grid-filter/useGridFilterState";

export interface ButtonFiltersControllerInput {
  gridFilterUserInput: IGridFilterUserInput;
  gridId?: SocketGroup; // Add optional grid identifier
}

export interface ButtonFiltersControllerState {
  isLoading: boolean;
  gridFilterUserInput: IGridFilterUserInput;
  dxGroupTypes: Record<DxCodeGroupType, DxCode[]>;
}

const gridFilterService = new GridFilterService();

export function useButtonFiltersController(gridId?: SocketGroup) {
  const userStore = useUserStore();
  const gridFilterState = useGridFilterState();

  const state = reactive<ButtonFiltersControllerState>(
    factoryButtonFiltersControllerState()
  );

  function init(input: ButtonFiltersControllerInput) {
    // Try to restore saved state first if gridId is provided
    if (gridId) {
      const savedState = gridFilterState.getFilterState(gridId);
      if (savedState) {
        // Restore the saved state with proper cloning
        state.isLoading = savedState.isLoading;
        state.gridFilterUserInput = simpleObjectClone(
          savedState.gridFilterUserInput
        );
        state.dxGroupTypes = simpleObjectClone(savedState.dxGroupTypes);
        // Ensure username is current
        state.gridFilterUserInput.MY_CASE_AND_PARAMEDIC_ON_SCENE.userName = formatUserDominoName(
          userStore.user.userName
        );
        return;
      }
    }

    // Fallback to provided input or default state
    state.gridFilterUserInput = simpleObjectClone(input.gridFilterUserInput);
    setGroupedDxCodes();

    state.gridFilterUserInput.MY_CASE_AND_PARAMEDIC_ON_SCENE.userName = formatUserDominoName(
      userStore.user.userName
    );
  }

  function saveCurrentState() {
    if (gridId) {
      // Clone the state to avoid mutation issues
      const stateToSave = {
        isLoading: state.isLoading,
        gridFilterUserInput: simpleObjectClone(state.gridFilterUserInput),
        dxGroupTypes: simpleObjectClone(state.dxGroupTypes)
      };
      gridFilterState.setFilterState(gridId, stateToSave);
    }
  }

  function setGroupedDxCodes(): Promise<void> {
    // TODO some API call...cache it?
    state.isLoading = true;

    return Promise.resolve().then(() => {
      state.dxGroupTypes = {
        "Ambulance Validation": ["DX333"],
        "Toxic Ingestion": ["DX325"],
        Other: []
      };
      state.isLoading = false;
    });
  }

  function reset() {
    state.gridFilterUserInput = gridFilterService.factoryGridFilterUserInput();
    // Clear saved state when resetting
    if (gridId) {
      gridFilterState.clearFilterState(gridId);
    }
  }

  function toggleCleoClientService(cleoClientService: CLEO_CLIENT_SERVICE) {
    const cleoClientServices = state.gridFilterUserInput.CLEO_CLIENT_SERVICE;

    const cleoClientServiceInternal = cleoClientService.toUpperCase();

    const isAlreadyInArray = cleoClientServices.includes(
      cleoClientServiceInternal
    );

    if (isAlreadyInArray) {
      state.gridFilterUserInput.CLEO_CLIENT_SERVICE = cleoClientServices.filter(
        (item: CLEO_CLIENT_SERVICE) => {
          // if Toxic Ingestion is selected also remove "" from the array
          if (cleoClientService === "TOXIC INGESTION" && item === "") {
            return false;
          }

          return item !== cleoClientServiceInternal;
        }
      );
    } else {
      state.gridFilterUserInput.CLEO_CLIENT_SERVICE.push(
        cleoClientServiceInternal
      );
      // If Toxic Ingestion is selected also add "" to the array
      if (cleoClientServiceInternal === "TOXIC INGESTION") {
        state.gridFilterUserInput.CLEO_CLIENT_SERVICE.push("");
      }
    }

    saveCurrentState(); // Save after change
  }

  function toggleDxCodeTypes(dxType: DxCodeGroupType) {
    let dxCodesToSet = state.dxGroupTypes[dxType];
    if (dxType === "Other") {
      dxCodesToSet = state.dxGroupTypes["Ambulance Validation"].concat(
        state.dxGroupTypes["Toxic Ingestion"]
      );
    }
    const include = dxType !== "Other";
    toggleDxCodes(dxCodesToSet, include);
  }

  function toggleDxCodes(dxCodesToToggle: Uppercase<string>[], include = true) {
    // if dxCodesToToggle is already in gridFilterUserInputInternal.value.DX_CODES then add it, else remove.;
    const dxCodes = state.gridFilterUserInput.DX.dxCodes;
    const isAlreadyInArray = dxCodesToToggle.every(dxCode =>
      dxCodes.includes(dxCode)
    );
    state.gridFilterUserInput.DX.include = include;
    if (isAlreadyInArray) {
      state.gridFilterUserInput.DX.dxCodes = dxCodes.filter(
        (item: string) => !dxCodesToToggle.includes(item)
      );
    } else {
      state.gridFilterUserInput.DX.dxCodes.push(...dxCodesToToggle);
    }
  }

  function toggleToxicIngestionAndEmpty() {
    if (state.gridFilterUserInput.TOXIC_INGESTION_AND_EMPTY) {
      state.gridFilterUserInput.TOXIC_INGESTION_AND_EMPTY = null;
    } else {
      state.gridFilterUserInput.TOXIC_INGESTION_AND_EMPTY = true;
    }
  }

  function toggleAmbulance() {
    if (state.gridFilterUserInput.AMBULANCE_CLEO_CLIENT_SERVICES) {
      state.gridFilterUserInput.AMBULANCE_CLEO_CLIENT_SERVICES = null;
    } else {
      state.gridFilterUserInput.AMBULANCE_CLEO_CLIENT_SERVICES = true;
    }
  }

  function toggleEDValidation() {
    if (state.gridFilterUserInput.ED_VALIDATION_CLEO_CLIENT_SERVICES) {
      state.gridFilterUserInput.ED_VALIDATION_CLEO_CLIENT_SERVICES = null;
    } else {
      state.gridFilterUserInput.ED_VALIDATION_CLEO_CLIENT_SERVICES = true;
    }
  }

  /**
   * Toggles the PDS_TRACED_AND_VERIFIED filter.  null is the default value: ALL
   * @param show
   */
  function toggleTracedAndVerified(show: boolean) {
    // if DX108 is already in gridFilterUserInputInternal.value.DX then add it, else remove.

    if (state.gridFilterUserInput.PDS_TRACED_AND_VERIFIED === show) {
      state.gridFilterUserInput.PDS_TRACED_AND_VERIFIED = null;
    } else {
      state.gridFilterUserInput.PDS_TRACED_AND_VERIFIED = show;
    }
    saveCurrentState(); // Save after change
  }

  function setAssignedDoctor(isAssignedDoctor: boolean) {
    if (state.gridFilterUserInput.ASSIGNED_TO === isAssignedDoctor) {
      state.gridFilterUserInput.ASSIGNED_TO = null;
    } else {
      state.gridFilterUserInput.ASSIGNED_TO = isAssignedDoctor;
    }
    saveCurrentState(); // Save after change
  }

  function toggleBreached(isBreached: boolean) {
    if (state.gridFilterUserInput.BREACHED === isBreached) {
      state.gridFilterUserInput.BREACHED = null;
    } else {
      state.gridFilterUserInput.BREACHED = isBreached;
    }
    saveCurrentState(); // Save after change
  }

  function toggleMyCasesParamedicOnScene() {
    if (state.gridFilterUserInput.MY_CASE_AND_PARAMEDIC_ON_SCENE.enabled) {
      state.gridFilterUserInput.MY_CASE_AND_PARAMEDIC_ON_SCENE.enabled = null;
    } else {
      state.gridFilterUserInput.MY_CASE_AND_PARAMEDIC_ON_SCENE.enabled = true;
    }
    saveCurrentState(); // Save after change
  }

  function toggleRequiresValidation() {
    if (state.gridFilterUserInput.REQUIRES_VALIDATION) {
      state.gridFilterUserInput.REQUIRES_VALIDATION = null;
    } else {
      state.gridFilterUserInput.REQUIRES_VALIDATION = true;
    }
    saveCurrentState(); // Save after change
  }

  function toggleFollowUp() {
    if (state.gridFilterUserInput.FOLLOW_UP) {
      state.gridFilterUserInput.FOLLOW_UP = null;
    } else {
      state.gridFilterUserInput.FOLLOW_UP = true;
    }
  }

  return {
    state,

    init,
    reset,
    saveCurrentState, // Expose for manual saving if needed
    toggleCleoClientService,
    toggleDxCodeTypes,
    toggleDxCodes,
    toggleToxicIngestionAndEmpty,
    toggleTracedAndVerified,
    setAssignedDoctor,
    toggleBreached,
    toggleAmbulance,
    toggleEDValidation,
    toggleMyCasesParamedicOnScene,
    toggleRequiresValidation,
    toggleFollowUp
  };
}
