import { mount, createLocalVue } from "@vue/test-utils";
import VueCompositionAPI from "@vue/composition-api";
import NonClinicalPreAssessment from "@/calls/common/dynamic-questions/fcms/non-clini-pre-assessment/ui/NonClinicalPreAssessment.vue";
import DynamicQuestionForm from "@/common/ui/dynamic-question/ui/DynamicQuestionForm.vue";
import { getNonCliniPreAssessmentQuestions } from "@/calls/common/dynamic-questions/fcms/non-clini-pre-assessment/api/non-clini-pre-assessment-api";
import { Question } from "@/common/ui/dynamic-question/models/dynamic-question-models";
import { mockNonCliniAssessment } from "../mock/mockNonCliniPreAssessment";
import { useDynamicQuestions } from "@/common/ui/dynamic-question/models/useDynamicQuestions";
import { simpleObjectClone } from "@/common/common-utils";

// Mock the API
jest.mock(
  "@/calls/common/dynamic-questions/fcms/non-clini-pre-assessment/api/non-clini-pre-assessment-api"
);

const mockGetNonCliniPreAssessmentQuestions = getNonCliniPreAssessmentQuestions as jest.MockedFunction<
  typeof getNonCliniPreAssessmentQuestions
>;

describe("NonClinicalPreAssessment", () => {
  let localVue: any;

  /*
  beforeEach(() => {
    localVue = createLocalVue();
    localVue.use(VueCompositionAPI);

    // Mock successful API response
    mockGetNonCliniPreAssessmentQuestions.mockResolvedValue([
      {
        id: "withPatient",
        type: "radio",
        label: "Q1: Are you with the patient?",
        options: ["Yes", "No"],
        mandatory: true,
        value: null,
        visible: true
      },
      {
        id: "canContactPatientDirectly",
        type: "radio",
        label: "Q2: Can we contact the patient directly?",
        options: ["Yes", "No"],
        mandatory: true,
        value: null,
        visible: false,
        condition: "answers['withPatient'] === 'No'",
        helpText:
          "Keep the caller on the line until successful contact with the patient has been made – if successful contact cannot be made, select No to this question."
      }
    ]);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should render loading state initially", async () => {
    const wrapper = mount(NonClinicalPreAssessment, {
      localVue,
      propsData: {
        service: "FCMS"
      }
    });

    expect(wrapper.find(".ic24-loading-spinner-large").exists()).toBe(true);
    expect(wrapper.text()).toContain("Loading assessment questions...");
  });

  it("should load questions on mount", async () => {
    const wrapper = mount(NonClinicalPreAssessment, {
      localVue,
      propsData: {
        service: "FCMS"
      }
    });

    await wrapper.vm.$nextTick();
    await wrapper.vm.$nextTick(); // Wait for async operations

    expect(mockGetNonCliniPreAssessmentQuestions).toHaveBeenCalledWith("FCMS");
  });

  it("should render questions when loaded", async () => {
    const wrapper = mount(NonClinicalPreAssessment, {
      localVue,
      propsData: {
        service: "FCMS"
      }
    });

    await wrapper.vm.$nextTick();
    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(DynamicQuestionForm).exists()).toBe(true);
    expect(wrapper.text()).toContain("FCMS Non-Clinical Pre-Assessment");
  });

  it("should show error state when API fails", async () => {
    mockGetNonCliniPreAssessmentQuestions.mockRejectedValue(
      new Error("API Error")
    );

    const wrapper = mount(NonClinicalPreAssessment, {
      localVue,
      propsData: {
        service: "FCMS"
      }
    });

    await wrapper.vm.$nextTick();
    await wrapper.vm.$nextTick();

    expect(wrapper.find(".ic24-alert--error").exists()).toBe(true);
    expect(wrapper.text()).toContain("Failed to load assessment questions");
  });

  it("should emit assessment-updated event when questions are answered", async () => {
    const wrapper = mount(NonClinicalPreAssessment, {
      localVue,
      propsData: {
        service: "FCMS"
      }
    });

    await wrapper.vm.$nextTick();
    await wrapper.vm.$nextTick();

    const dynamicQuestionForm = wrapper.findComponent(DynamicQuestionForm);
    const mockOutput = {
      questions: [],
      answers: { withPatient: "Yes" },
      isValid: false,
      questionsThatNeedAnswer: []
    };

    // Simulate the DynamicQuestionForm emitting an input event
    dynamicQuestionForm.vm.$emit("input", mockOutput);

    await wrapper.vm.$nextTick();

    expect(wrapper.emitted("assessment-updated")).toBeTruthy();
    expect(wrapper.emitted("assessment-updated")![0]).toEqual([mockOutput]);
  });

  it("should emit assessment-complete event when all mandatory questions are answered", async () => {
    const wrapper = mount(NonClinicalPreAssessment, {
      localVue,
      propsData: {
        service: "FCMS"
      }
    });

    await wrapper.vm.$nextTick();
    await wrapper.vm.$nextTick();

    const dynamicQuestionForm = wrapper.findComponent(DynamicQuestionForm);
    const mockOutput = {
      questions: [],
      answers: { withPatient: "Yes" },
      isValid: true,
      questionsThatNeedAnswer: []
    };

    // Simulate the DynamicQuestionForm emitting an input event with valid assessment
    dynamicQuestionForm.vm.$emit("input", mockOutput);

    await wrapper.vm.$nextTick();

    expect(wrapper.emitted("assessment-complete")).toBeTruthy();
    expect(wrapper.emitted("assessment-complete")![0]).toEqual([mockOutput]);
  });

  it("should show validation errors when questions are not answered", async () => {
    const wrapper = mount(NonClinicalPreAssessment, {
      localVue,
      propsData: {
        service: "FCMS"
      }
    });

    await wrapper.vm.$nextTick();
    await wrapper.vm.$nextTick();

    const dynamicQuestionForm = wrapper.findComponent(DynamicQuestionForm);
    const mockOutput = {
      questions: [],
      answers: {},
      isValid: false,
      questionsThatNeedAnswer: [
        {
          id: "withPatient",
          label: "Q1: Are you with the patient?",
          type: "radio",
          mandatory: true,
          visible: true
        }
      ]
    };

    // Simulate the DynamicQuestionForm emitting an input event with validation errors
    dynamicQuestionForm.vm.$emit("input", mockOutput);

    await wrapper.vm.$nextTick();

    expect(wrapper.find(".ic24-alert--warning").exists()).toBe(true);
    expect(wrapper.text()).toContain(
      "Please answer the following mandatory questions"
    );
    expect(wrapper.text()).toContain("Q1: Are you with the patient?");
  });

  it("should show assessment complete state when all questions are answered", async () => {
    const wrapper = mount(NonClinicalPreAssessment, {
      localVue,
      propsData: {
        service: "FCMS"
      }
    });

    await wrapper.vm.$nextTick();
    await wrapper.vm.$nextTick();

    const dynamicQuestionForm = wrapper.findComponent(DynamicQuestionForm);
    const mockOutput = {
      questions: [],
      answers: { withPatient: "Yes" },
      isValid: true,
      questionsThatNeedAnswer: []
    };

    // Simulate the DynamicQuestionForm emitting an input event with valid assessment
    dynamicQuestionForm.vm.$emit("input", mockOutput);

    await wrapper.vm.$nextTick();

    expect(wrapper.find(".ic24-alert--success").exists()).toBe(true);
    expect(wrapper.text()).toContain("Assessment Complete");
    expect(wrapper.text()).toContain(
      "All mandatory questions have been answered"
    );
  });

  it("should retry loading questions when retry button is clicked", async () => {
    mockGetNonCliniPreAssessmentQuestions.mockRejectedValueOnce(
      new Error("API Error")
    );
    mockGetNonCliniPreAssessmentQuestions.mockResolvedValueOnce([]);

    const wrapper = mount(NonClinicalPreAssessment, {
      localVue,
      propsData: {
        service: "FCMS"
      }
    });

    await wrapper.vm.$nextTick();
    await wrapper.vm.$nextTick();

    // Should show error state
    expect(wrapper.find(".ic24-alert--error").exists()).toBe(true);

    // Click retry button
    const retryButton = wrapper.find(".ic24-button--secondary");
    await retryButton.trigger("click");

    await wrapper.vm.$nextTick();
    await wrapper.vm.$nextTick();

    // Should have called the API again
    expect(mockGetNonCliniPreAssessmentQuestions).toHaveBeenCalledTimes(2);
  });

  it("should accept service prop", async () => {
    const wrapper = mount(NonClinicalPreAssessment, {
      localVue,
      propsData: {
        service: "TEST_SERVICE"
      }
    });

    await wrapper.vm.$nextTick();
    await wrapper.vm.$nextTick();

    expect(mockGetNonCliniPreAssessmentQuestions).toHaveBeenCalledWith(
      "TEST_SERVICE"
    );
  });

  it("should default to FCMS service when no service prop is provided", async () => {
    const wrapper = mount(NonClinicalPreAssessment, {
      localVue
    });

    await wrapper.vm.$nextTick();
    await wrapper.vm.$nextTick();

    expect(mockGetNonCliniPreAssessmentQuestions).toHaveBeenCalledWith("FCMS");
  });
  */

  describe("Dynamic Question Flow Tests", () => {
    it("should navigate Q1 Yes -> Q3", () => {
      const questions = mockNonCliniAssessment;
      const dynamicQuestionsController = useDynamicQuestions();
      dynamicQuestionsController.init(questions);

      const question = { ...questions.find(q => q.id === "withPatient")! };
      question.value = "Yes";
      dynamicQuestionsController.onQuestionAnswered(question);

      expect(dynamicQuestionsController.state.currentQuestionId).toBe("callerIsClinician");
      // Check that questions after Q3 are hidden
      expect(dynamicQuestionsController.state.questions.find(q => q.id === "patientEndOfLifeCare")!.visible).toBe(false);
      expect(dynamicQuestionsController.state.questions.find(q => q.id === "selectPriority")!.visible).toBe(false);
    });

    it("should navigate Q1 No -> Q2 No -> Q3", () => {
      const questions = mockNonCliniAssessment;
      const dynamicQuestionsController = useDynamicQuestions();
      dynamicQuestionsController.init(questions);

      let question = { ...questions.find(q => q.id === "withPatient")! };
      question.value = "No";
      dynamicQuestionsController.onQuestionAnswered(question);
      expect(dynamicQuestionsController.state.currentQuestionId).toBe("canContactPatientDirectly");

      question = { ...questions.find(q => q.id === "canContactPatientDirectly")! };
      question.value = "No";
      dynamicQuestionsController.onQuestionAnswered(question);
      expect(dynamicQuestionsController.state.currentQuestionId).toBe("callerIsClinician");
    });

    it("should navigate Q1 No -> Q2 Yes (end)", () => {
      const questions = mockNonCliniAssessment;
      const dynamicQuestionsController = useDynamicQuestions();
      dynamicQuestionsController.init(questions);

      let question = { ...questions.find(q => q.id === "withPatient")! };
      question.value = "No";
      dynamicQuestionsController.onQuestionAnswered(question);
      expect(dynamicQuestionsController.state.currentQuestionId).toBe("canContactPatientDirectly");

      question = { ...questions.find(q => q.id === "canContactPatientDirectly")! };
      question.value = "Yes";
      dynamicQuestionsController.onQuestionAnswered(question);
      // Since nextQuestion is null, currentQuestionId remains the same
      expect(dynamicQuestionsController.state.currentQuestionId).toBe("canContactPatientDirectly");
    });

    it("should navigate Q3 Yes -> Q12", () => {
      const questions = mockNonCliniAssessment;
      const dynamicQuestionsController = useDynamicQuestions();
      dynamicQuestionsController.init(questions);

      // Start from Q3
      dynamicQuestionsController.state.currentQuestionId = "callerIsClinician";

      const question = { ...dynamicQuestionsController.state.questions.find(q => q.id === "callerIsClinician")! };
      question.value = "Yes";
      dynamicQuestionsController.onQuestionAnswered(question);
      expect(dynamicQuestionsController.state.currentQuestionId).toBe("selectPriority");
    });

    it("should navigate Q3 No -> Q4 No -> Q5", () => {
      const questions = mockNonCliniAssessment;
      const dynamicQuestionsController = useDynamicQuestions();
      dynamicQuestionsController.init(questions);

      // Start from Q3
      dynamicQuestionsController.state.currentQuestionId = "callerIsClinician";

      let question = { ...dynamicQuestionsController.state.questions.find(q => q.id === "callerIsClinician")! };
      question.value = "No";
      dynamicQuestionsController.onQuestionAnswered(question);
      expect(dynamicQuestionsController.state.currentQuestionId).toBe("patientEndOfLifeCare");

      question = { ...dynamicQuestionsController.state.questions.find(q => q.id === "patientEndOfLifeCare")! };
      question.value = "No";
      dynamicQuestionsController.onQuestionAnswered(question);
      expect(dynamicQuestionsController.state.currentQuestionId).toBe("patientBreathingConscious");
    });

    it("should navigate Q4 Yes (end)", () => {
      const questions = mockNonCliniAssessment;
      const dynamicQuestionsController = useDynamicQuestions();
      dynamicQuestionsController.init(questions);

      // Start from Q4
      dynamicQuestionsController.state.currentQuestionId = "patientEndOfLifeCare";

      const question = { ...dynamicQuestionsController.state.questions.find(q => q.id === "patientEndOfLifeCare")! };
      question.value = "Yes";
      dynamicQuestionsController.onQuestionAnswered(question);
      // nextQuestion null, stays on Q4
      expect(dynamicQuestionsController.state.currentQuestionId).toBe("patientEndOfLifeCare");
    });

    it("should navigate Q5 Yes -> Q7", () => {
      const questions = mockNonCliniAssessment;
      const dynamicQuestionsController = useDynamicQuestions();
      dynamicQuestionsController.init(questions);

      // Start from Q5
      dynamicQuestionsController.state.currentQuestionId = "patientBreathingConscious";

      const question = { ...dynamicQuestionsController.state.questions.find(q => q.id === "patientBreathingConscious")! };
      question.value = "Yes";
      dynamicQuestionsController.onQuestionAnswered(question);
      expect(dynamicQuestionsController.state.currentQuestionId).toBe("majorBloodLoss");
    });

    it("should navigate Q5 No -> Q6", () => {
      const questions = mockNonCliniAssessment;
      const dynamicQuestionsController = useDynamicQuestions();
      dynamicQuestionsController.init(questions);

      // Start from Q5
      dynamicQuestionsController.state.currentQuestionId = "patientBreathingConscious";

      const question = { ...dynamicQuestionsController.state.questions.find(q => q.id === "patientBreathingConscious")! };
      question.value = "No";
      dynamicQuestionsController.onQuestionAnswered(question);
      expect(dynamicQuestionsController.state.currentQuestionId).toBe("patientCall999");
      // Check that questions after Q6 are hidden
      expect(dynamicQuestionsController.state.questions.find(q => q.id === "majorBloodLoss")!.visible).toBe(false);
      expect(dynamicQuestionsController.state.questions.find(q => q.id === "selectPriority")!.visible).toBe(false);
    });

    it("should navigate Q7 Yes -> Q6", () => {
      const questions = mockNonCliniAssessment;
      const dynamicQuestionsController = useDynamicQuestions();
      dynamicQuestionsController.init(questions);

      // Start from Q7
      dynamicQuestionsController.state.currentQuestionId = "majorBloodLoss";

      const question = { ...dynamicQuestionsController.state.questions.find(q => q.id === "majorBloodLoss")! };
      question.value = "Yes";
      dynamicQuestionsController.onQuestionAnswered(question);
      expect(dynamicQuestionsController.state.currentQuestionId).toBe("patientCall999");
    });

    it("should navigate Q7 No -> Q8", () => {
      const questions = mockNonCliniAssessment;
      const dynamicQuestionsController = useDynamicQuestions();
      dynamicQuestionsController.init(questions);

      // Start from Q7
      dynamicQuestionsController.state.currentQuestionId = "majorBloodLoss";

      const question = { ...dynamicQuestionsController.state.questions.find(q => q.id === "majorBloodLoss")! };
      question.value = "No";
      dynamicQuestionsController.onQuestionAnswered(question);
      expect(dynamicQuestionsController.state.currentQuestionId).toBe("tooBreathless");
    });

    it("should navigate Q8 Yes -> Q8a", () => {
      const questions = mockNonCliniAssessment;
      const dynamicQuestionsController = useDynamicQuestions();
      dynamicQuestionsController.init(questions);

      // Start from Q8
      dynamicQuestionsController.state.currentQuestionId = "tooBreathless";

      const question = { ...dynamicQuestionsController.state.questions.find(q => q.id === "tooBreathless")! };
      question.value = "Yes";
      dynamicQuestionsController.onQuestionAnswered(question);
      expect(dynamicQuestionsController.state.currentQuestionId).toBe("normallyBreathless");
    });

    it("should navigate Q8 No -> Q9", () => {
      const questions = mockNonCliniAssessment;
      const dynamicQuestionsController = useDynamicQuestions();
      dynamicQuestionsController.init(questions);

      // Start from Q8
      dynamicQuestionsController.state.currentQuestionId = "tooBreathless";

      const question = { ...dynamicQuestionsController.state.questions.find(q => q.id === "tooBreathless")! };
      question.value = "No";
      dynamicQuestionsController.onQuestionAnswered(question);
      expect(dynamicQuestionsController.state.currentQuestionId).toBe("chestPain");
    });

    it("should navigate Q8a Yes -> Q9", () => {
      const questions = mockNonCliniAssessment;
      const dynamicQuestionsController = useDynamicQuestions();
      dynamicQuestionsController.init(questions);

      // Start from Q8a
      dynamicQuestionsController.state.currentQuestionId = "normallyBreathless";

      const question = { ...dynamicQuestionsController.state.questions.find(q => q.id === "normallyBreathless")! };
      question.value = "Yes";
      dynamicQuestionsController.onQuestionAnswered(question);
      expect(dynamicQuestionsController.state.currentQuestionId).toBe("chestPain");
    });

    it("should navigate Q8a No -> Q6", () => {
      const questions = mockNonCliniAssessment;
      const dynamicQuestionsController = useDynamicQuestions();
      dynamicQuestionsController.init(questions);

      // Start from Q8a
      dynamicQuestionsController.state.currentQuestionId = "normallyBreathless";

      const question = { ...dynamicQuestionsController.state.questions.find(q => q.id === "normallyBreathless")! };
      question.value = "No";
      dynamicQuestionsController.onQuestionAnswered(question);
      expect(dynamicQuestionsController.state.currentQuestionId).toBe("patientCall999");
    });

    it("should navigate Q9 Yes -> Q10", () => {
      const questions = mockNonCliniAssessment;
      const dynamicQuestionsController = useDynamicQuestions();
      dynamicQuestionsController.init(questions);

      // Start from Q9
      dynamicQuestionsController.state.currentQuestionId = "chestPain";

      const question = { ...dynamicQuestionsController.state.questions.find(q => q.id === "chestPain")! };
      question.value = "Yes";
      dynamicQuestionsController.onQuestionAnswered(question);
      expect(dynamicQuestionsController.state.currentQuestionId).toBe("crushingChestPain");
    });

    it("should navigate Q9 No -> Q11", () => {
      const questions = mockNonCliniAssessment;
      const dynamicQuestionsController = useDynamicQuestions();
      dynamicQuestionsController.init(questions);

      // Start from Q9
      dynamicQuestionsController.state.currentQuestionId = "chestPain";

      const question = { ...dynamicQuestionsController.state.questions.find(q => q.id === "chestPain")! };
      question.value = "No";
      dynamicQuestionsController.onQuestionAnswered(question);
      expect(dynamicQuestionsController.state.currentQuestionId).toBe("strokeSymptoms");
    });

    it("should navigate Q10 Yes -> Q6", () => {
      const questions = mockNonCliniAssessment;
      const dynamicQuestionsController = useDynamicQuestions();
      dynamicQuestionsController.init(questions);

      // Start from Q10
      dynamicQuestionsController.state.currentQuestionId = "crushingChestPain";

      const question = { ...dynamicQuestionsController.state.questions.find(q => q.id === "crushingChestPain")! };
      question.value = "Yes";
      dynamicQuestionsController.onQuestionAnswered(question);
      expect(dynamicQuestionsController.state.currentQuestionId).toBe("patientCall999");
    });

    it("should navigate Q10 No -> Q11", () => {
      const questions = mockNonCliniAssessment;
      const dynamicQuestionsController = useDynamicQuestions();
      dynamicQuestionsController.init(questions);

      // Start from Q10
      dynamicQuestionsController.state.currentQuestionId = "crushingChestPain";

      const question = { ...dynamicQuestionsController.state.questions.find(q => q.id === "crushingChestPain")! };
      question.value = "No";
      dynamicQuestionsController.onQuestionAnswered(question);
      expect(dynamicQuestionsController.state.currentQuestionId).toBe("strokeSymptoms");
    });

    it("should navigate Q11 Yes -> Q6", () => {
      const questions = mockNonCliniAssessment;
      const dynamicQuestionsController = useDynamicQuestions();
      dynamicQuestionsController.init(questions);

      // Start from Q11
      dynamicQuestionsController.state.currentQuestionId = "strokeSymptoms";

      const question = { ...dynamicQuestionsController.state.questions.find(q => q.id === "strokeSymptoms")! };
      question.value = "Yes";
      dynamicQuestionsController.onQuestionAnswered(question);
      expect(dynamicQuestionsController.state.currentQuestionId).toBe("patientCall999");
    });

    it("should navigate Q11 No -> Q12", () => {
      const questions = mockNonCliniAssessment;
      const dynamicQuestionsController = useDynamicQuestions();
      dynamicQuestionsController.init(questions);

      // Start from Q11
      dynamicQuestionsController.state.currentQuestionId = "strokeSymptoms";

      const question = { ...dynamicQuestionsController.state.questions.find(q => q.id === "strokeSymptoms")! };
      question.value = "No";
      dynamicQuestionsController.onQuestionAnswered(question);
      expect(dynamicQuestionsController.state.currentQuestionId).toBe("selectPriority");
    });
  });
});
