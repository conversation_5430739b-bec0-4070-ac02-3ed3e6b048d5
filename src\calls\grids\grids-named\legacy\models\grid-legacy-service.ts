import {
  GridLegacyCallSummary,
  TextSimpleInteger
} from "@/calls/grids/grids-named/legacy/models/grid-legacy-models";
import {
  CallStatusValue,
  ICleoCallSummary
} from "@/calls/summary/call-summarry-models";
import { GENDER, GENDER_CONFIG, legacyGenderMap } from "@/common/common-models";
import { toLocalISOWithOffset } from "@/common/common-utils";

export function mapGridLegacyCallSummaryToICleoCallSummary(
  gridLegacyCallSummary: GridLegacyCallSummary
): ICleoCallSummary {
  return {
    AllViewExclude: null,
    AllViewInclude: null,

    CallNo: Number(gridLegacyCallSummary.CallNo),
    CallService: {
      Id: 0,
      Description: gridLegacyCallSummary.CallService,
      Type: ""
    },
    IucContract: {
      Id: 0,
      Description: gridLegacyCallSummary.IUC_Contract,
      Type: gridLegacyCallSummary.IUC_Contract.length > 0 ? "CAS" : ""
    },
    cleoClientService: gridLegacyCallSummary.cleoClientService,
    CallPostCode: gridLegacyCallSummary.CallPostCode,
    // CallMf: GENDER;
    CallGenderId: legacyGenderMap[gridLegacyCallSummary.CallMF],
    CallNhsNo: gridLegacyCallSummary.CallNHSNo,

    BreachActualTime: gridLegacyCallSummary.BreachActualTime,
    BreachWarnActualTime: gridLegacyCallSummary.BreachWarnActualTime,
    BreachPreActualTime: gridLegacyCallSummary.BreachPreActualTime,
    BreachPriority:
      gridLegacyCallSummary.BreachPriority.length > 0
        ? Number(gridLegacyCallSummary.BreachPriority)
        : 0, //   from Breach config.
    // BreachKey: string; //  Needs to go.
    ApplyBreach: gridLegacyCallSummary.ApplyBreach === "1",
    BreachLevel1Mins: Number(gridLegacyCallSummary.BreachLevel1Mins),

    CallWithBaseAckTime: gridLegacyCallSummary.CallWithBaseAckTime,
    CallUrgentYn: gridLegacyCallSummary.CallUrgentYN.toUpperCase() === "YES",
    CallReceivedTime: gridLegacyCallSummary.CallReceivedTime,
    // CallReceivedISO: string; //  ...coming th

    CallStatusValue: Number(
      gridLegacyCallSummary.CallStatusValue
    ) as CallStatusValue,
    IsLocked: gridLegacyCallSummary.IsLocked,

    CallForename: gridLegacyCallSummary.CallForename,
    CallSurname: gridLegacyCallSummary.CallSurname,

    CallPractice: gridLegacyCallSummary.CallPractice || "",

    Itk111Online: gridLegacyCallSummary.ITK_111_Online === "1",

    ChFinalDispositionCode: gridLegacyCallSummary.CHFinalDispositionCode,
    ChFinalDispositionDescription:
      gridLegacyCallSummary.CHFinalDispositionDescription,

    FinalDispositionCode: gridLegacyCallSummary.FinalDispositionCode,
    FinalDispositionDescription:
      gridLegacyCallSummary.FinalDispositionDescription,

    DispatchVehicle: gridLegacyCallSummary.Dispatch_Vehicle,
    WalkIn: gridLegacyCallSummary.WalkIn === "1",

    CallCallback:
      gridLegacyCallSummary.CallCallback.length > 0
        ? Number(gridLegacyCallSummary.CallCallback)
        : 0,
    CallWarmTransferred:
      gridLegacyCallSummary.CallWarmTransferred.toUpperCase() === "YES",
    PdsTracedAndVerified:
      gridLegacyCallSummary.PDSTracedAndVerified.toUpperCase() === "YES",
    PDSAdminTrace: gridLegacyCallSummary.PDSAdminTrace
      ? gridLegacyCallSummary.PDSAdminTrace
      : "",

    CallAddress1: gridLegacyCallSummary.CallAddress1,
    CallAddress2: gridLegacyCallSummary.CallAddress2,
    CallAddress3: gridLegacyCallSummary.CallAddress3,
    CallAddress4: gridLegacyCallSummary.CallAddress4,
    CallTown: gridLegacyCallSummary.CallTown,

    // CallDobIso: string | null;

    // E.g. 7 yrs
    CallAge: Number(gridLegacyCallSummary.CallAge.split(" ")[0]),
    CallAgeClass: gridLegacyCallSummary.CallAge.split(" ")[1],
    CallDobIso: gridLegacyCallSummary.CallDobIso,

    CallCName: gridLegacyCallSummary.CallCName,
    CallCRel: gridLegacyCallSummary.CallCRel,

    CallCreatedBy: gridLegacyCallSummary.CallCreatedBy,
    CallArrivedTime: gridLegacyCallSummary.CallArrivedTime,

    CallDoctorName: gridLegacyCallSummary.CallDoctorName,

    // TODO Do we have classifications in ref data?
    CallClassification: {
      Id: 0,
      Description: gridLegacyCallSummary.CallClassification
    },
    //  TODO ??
    CallSubClassification: {
      Id: 0,
      Description: gridLegacyCallSummary.CSC
    },

    CallTelNo: gridLegacyCallSummary.CallTelNo,
    CallTelNoAlt1: gridLegacyCallSummary.CallTelNoAlt_1,
    CallTelNoAltType1: gridLegacyCallSummary.CallTelNoAltType_1,
    CallTelNo_R: gridLegacyCallSummary.CallTelNo_R,

    CallSymptoms: gridLegacyCallSummary.CallSymptoms,

    //118:21 Type '() => string' is not assignable to type 'string'.
    Call1StContact: ((): string => {
      const iso = toLocalISOWithOffset(gridLegacyCallSummary.Call1stContact);
      return iso.error === "" ? iso.iso : "";
    })(),
    Call1StContactPathways: ((): string => {
      const iso = toLocalISOWithOffset(
        gridLegacyCallSummary.Call1stContactPathways
      );
      return iso.error === "" ? iso.iso : "";
    })(),
    PathwaysCaseId: gridLegacyCallSummary.PathwaysCaseId,

    PatientContactCode: gridLegacyCallSummary.PatientContactCode,
    PatientContactCodeCount: Number(
      gridLegacyCallSummary.PatientContactCode_count
    ),
    //  TODO ??
    LastFailedContactTime:
      gridLegacyCallSummary.PatientContactCode_Current_ForView,

    CallAppointmentTime: gridLegacyCallSummary.CallAppointmentTime,
    DateAppointmentStart: gridLegacyCallSummary.dateAppointmentStart,
    CareConnectAppointmentStart:
      gridLegacyCallSummary.CareConnectAppointmentStart,

    AFT_UNABLE_REASON: gridLegacyCallSummary.AFT_UNABLE_REASON,

    ComfortSentServiceTime: gridLegacyCallSummary.Comfort_SENT_SERVICE_TIME,
    ComfortSmsTime: gridLegacyCallSummary.Comfort_SMS_TIME,
    ComfortSentService2Time: gridLegacyCallSummary.Comfort_SENT_SERVICE2_TIME,
    ComfortSmsTime2: gridLegacyCallSummary.Comfort_SMS_TIME2,

    CourtesyTime: gridLegacyCallSummary.Courtesy_Time,
    CourtesyUser: gridLegacyCallSummary.Courtesy_User,
    CourtesyCount:
      gridLegacyCallSummary.Courtesy_Count.length > 0
        ? Number(gridLegacyCallSummary.Courtesy_Count)
        : 0,
    CourtesyContact: isCourtesyContact(gridLegacyCallSummary.Courtesy_Contact),

    CliniHighPriority:
      gridLegacyCallSummary.CliniHighPriority.length > 0 &&
      gridLegacyCallSummary.CliniHighPriority === "1",

    //  TODO Adapter not sending.
    CallInformationalOutcomes: gridLegacyCallSummary.CallInformationalOutcomes,

    //  TODO
    CallInformationalSubOutcomes: "",
    CallInformationalOutcomesComment:
      gridLegacyCallSummary.CallInformationalOutcomesComment,

    //  New/unmapped fields
    // CaseCallBackRequired: "False"

    DutyBase: gridLegacyCallSummary.DutyBase,
    StartConsultationPerformed:
      gridLegacyCallSummary.StartConsultationPerformed.length > 0,

    Long: 0,
    Lat: 0,

    CasValidationCount:
      gridLegacyCallSummary.CasValidationCount &&
      gridLegacyCallSummary.CasValidationCount.length > 0
        ? Number(gridLegacyCallSummary.CasValidationCount)
        : 0,
    CasValidationUser:
      gridLegacyCallSummary.CasValidationUser &&
      gridLegacyCallSummary.CasValidationUser.length > 0
        ? gridLegacyCallSummary.CasValidationUser
        : "",
    CasValidationTime:
      gridLegacyCallSummary.CasValidationTime &&
      gridLegacyCallSummary.CasValidationTime.length > 0
        ? gridLegacyCallSummary.CasValidationTime
        : "",
    CasValidationReason: gridLegacyCallSummary.CasValidationReason || "",
    CaseComments: gridLegacyCallSummary.Call_CaseComments
      ? gridLegacyCallSummary.Call_CaseComments
      : "",

    LinkedCallID: gridLegacyCallSummary.Linked_Call_ID,
    COMPLETE_PREVENT: gridLegacyCallSummary.COMPLETE_PREVENT,
    FOLLOW_UP_URGENT: gridLegacyCallSummary.FOLLOW_UP_URGENT,
    FOLLOW_UP_Active: gridLegacyCallSummary.FOLLOW_UP_Active,
    Call_HCP: gridLegacyCallSummary.Call_HCP,
    OversightValidationType: gridLegacyCallSummary.OversightValidationType
      ? gridLegacyCallSummary.OversightValidationType
      : "",

    OVERSIGHT_BASE_TRIAGE_TYPE:
      gridLegacyCallSummary.OVERSIGHT_BASE_TRIAGE_TYPE,

    SMS_HAS: gridLegacyCallSummary.SMS_HAS === "1",
    SMS_SENT: gridLegacyCallSummary.SMS_SENT === "1",
    SMS_LATEST_AT: gridLegacyCallSummary.SMS_LATEST_AT,
    SMS_LATEST_USER: gridLegacyCallSummary.SMS_LATEST_USER,
    SMS_LATEST_MESSAGE: gridLegacyCallSummary.SMS_LATEST_MESSAGE,

    Cpl_supportTypeRequired: gridLegacyCallSummary.Cpl_supportTypeRequired,

    GOODSAM_IMAGE_STATUS: gridLegacyCallSummary.GOODSAM_IMAGE_STATUS
      ? gridLegacyCallSummary.GOODSAM_IMAGE_STATUS
      : "",

    CallFAction: gridLegacyCallSummary.CallFAction || "",
    Cpl_furtherActionGPText:
      gridLegacyCallSummary.Cpl_furtherActionGPText || "",

    PLS_REASON: gridLegacyCallSummary.PLS_REASON,
    PLS_TIME: gridLegacyCallSummary.PLS_TIME,
    PLS_USER: gridLegacyCallSummary.PLS_USER,
    PLS_ACTION: gridLegacyCallSummary.PLS_ACTION,
    PLS_ACTIONTEXT: gridLegacyCallSummary.PLS_ACTIONTEXT
  };
}

export function isCourtesyContact(Courtesy_Contact: string): boolean {
  return Courtesy_Contact === "1" || Courtesy_Contact === "true";
}

export function mapICleoCallSummaryToGridLegacyCallSummary(
  cleoCallSummary: ICleoCallSummary
): GridLegacyCallSummary {
  return {
    CallNo: cleoCallSummary.CallNo.toString(),
    CallService: cleoCallSummary.CallService?.Description || "",
    IUC_Contract: cleoCallSummary.IucContract?.Description || "",
    cleoClientService: cleoCallSummary.cleoClientService || "",
    CallPostCode: cleoCallSummary.CallPostCode || "",
    CallMF: GENDER_CONFIG[cleoCallSummary.CallGenderId] as GENDER,
    CallNHSNo: cleoCallSummary.CallNhsNo || "",

    BreachActualTime: cleoCallSummary.BreachActualTime || "",
    BreachWarnActualTime: cleoCallSummary.BreachWarnActualTime || "",
    BreachPreActualTime: cleoCallSummary.BreachPreActualTime || "",
    BreachPriority:
      (cleoCallSummary.BreachPriority?.toString() as TextSimpleInteger) || "",
    ApplyBreach: cleoCallSummary.ApplyBreach ? "1" : "0",
    BreachLevel1Mins: cleoCallSummary.BreachLevel1Mins?.toString() || "",

    CallWithBaseAckTime: cleoCallSummary.CallWithBaseAckTime || "",
    CallUrgentYN: cleoCallSummary.CallUrgentYn ? "Yes" : "No",
    CallReceivedTime: cleoCallSummary.CallReceivedTime || "",
    CallStatusValue: cleoCallSummary.CallStatusValue?.toString() || "",
    IsLocked: cleoCallSummary.IsLocked || "",

    CallForename: cleoCallSummary.CallForename || "",
    CallSurname: cleoCallSummary.CallSurname || "",

    ITK_111_Online: cleoCallSummary.Itk111Online ? "1" : "0",

    CHFinalDispositionCode: cleoCallSummary.ChFinalDispositionCode || "",
    CHFinalDispositionDescription:
      cleoCallSummary.ChFinalDispositionDescription || "",
    FinalDispositionCode: cleoCallSummary.FinalDispositionCode || "",
    FinalDispositionDescription:
      cleoCallSummary.FinalDispositionDescription || "",

    Dispatch_Vehicle: cleoCallSummary.DispatchVehicle || "",
    WalkIn: cleoCallSummary.WalkIn ? "1" : "0",

    CallCallback:
      (cleoCallSummary.CallCallback?.toString() as TextSimpleInteger) || "",
    CallWarmTransferred: cleoCallSummary.CallWarmTransferred ? "Yes" : "No",
    PDSTracedAndVerified: cleoCallSummary.PdsTracedAndVerified ? "Yes" : "No",
    PDSAdminTrace: cleoCallSummary.PDSAdminTrace,

    CallAddress1: cleoCallSummary.CallAddress1 || "",
    CallAddress2: cleoCallSummary.CallAddress2 || "",
    CallAddress3: cleoCallSummary.CallAddress3 || "",
    CallAddress4: cleoCallSummary.CallAddress4 || "",
    CallTown: cleoCallSummary.CallTown || "",

    CallAge: `${cleoCallSummary.CallAge || ""} ${cleoCallSummary.CallAgeClass ||
      ""}`.trim(),

    CallCName: cleoCallSummary.CallCName || "",
    CallCRel: cleoCallSummary.CallCRel || "",

    CallCreatedBy: cleoCallSummary.CallCreatedBy || "",
    CallArrivedTime: cleoCallSummary.CallArrivedTime || "",
    CallDoctorName: cleoCallSummary.CallDoctorName || "",

    CallClassification: cleoCallSummary.CallClassification?.Description || "",

    CallTelNoAlt_1: cleoCallSummary.CallTelNoAlt1 || "",
    CallTelNoAltType_1: cleoCallSummary.CallTelNoAltType1 || "",

    CallSymptoms: cleoCallSummary.CallSymptoms || "",

    Call1stContact: cleoCallSummary.Call1StContact || "",
    Call1stContactPathways: cleoCallSummary.Call1StContactPathways || "",
    PathwaysCaseId: cleoCallSummary.PathwaysCaseId || "",

    PatientContactCode: cleoCallSummary.PatientContactCode || "",
    PatientContactCode_count:
      cleoCallSummary.PatientContactCodeCount?.toString() || "",

    CallAppointmentTime: cleoCallSummary.CallAppointmentTime || "",
    dateAppointmentStart: cleoCallSummary.DateAppointmentStart || "",
    CareConnectAppointmentStart:
      cleoCallSummary.CareConnectAppointmentStart || "",
    AFT_UNABLE_REASON: cleoCallSummary.AFT_UNABLE_REASON || "",

    Comfort_SENT_SERVICE_TIME: cleoCallSummary.ComfortSentServiceTime || "",
    Comfort_SMS_TIME: cleoCallSummary.ComfortSmsTime || "",
    Comfort_SENT_SERVICE2_TIME: cleoCallSummary.ComfortSentService2Time || "",
    Comfort_SMS_TIME2: cleoCallSummary.ComfortSmsTime2 || "",

    Courtesy_Time: cleoCallSummary.CourtesyTime || "",
    Courtesy_User: cleoCallSummary.CourtesyUser || "",
    Courtesy_Count:
      (cleoCallSummary.CourtesyCount?.toString() as TextSimpleInteger) || "",
    Courtesy_Contact: cleoCallSummary.CourtesyContact ? "1" : "",

    CliniHighPriority: cleoCallSummary.CliniHighPriority ? "1" : "",

    CallInformationalOutcomes: cleoCallSummary.CallInformationalOutcomes || "",
    CallInformationalOutcomesComment:
      cleoCallSummary.CallInformationalOutcomesComment || "",

    DutyBase: cleoCallSummary.DutyBase || "",
    StartConsultationPerformed: cleoCallSummary.StartConsultationPerformed
      ? "1"
      : "",

    CasValidationCount: cleoCallSummary.CasValidationCount?.toString() || "",
    CasValidationUser: cleoCallSummary.CasValidationUser || "",
    CasValidationTime: cleoCallSummary.CasValidationTime || "",
    CasValidationReason: cleoCallSummary.CasValidationReason || "",
    Call_CaseComments: cleoCallSummary.CaseComments || "",

    CallFAction: cleoCallSummary.CallFAction || "",
    Cpl_furtherActionGPText: cleoCallSummary.Cpl_furtherActionGPText || "",

    PLS_REASON: cleoCallSummary.PLS_REASON || "",
    PLS_TIME: cleoCallSummary.PLS_TIME || "",
    PLS_USER: cleoCallSummary.PLS_USER || "",
    PLS_ACTION: cleoCallSummary.PLS_ACTION || "",
    PLS_ACTIONTEXT: cleoCallSummary.PLS_ACTIONTEXT || ""
  } as GridLegacyCallSummary;
}
