import {
  ILegacyCleoServerResponse,
  ILegacyDojoResponse
} from "@/common/cleo-legacy-models";
import { IPafAddress } from "@/paf/paf-models";

const pafMockData = require("@/paf/paf-mock-data.json");

export class PafData {
  public getPafData(
    postCode: string
  ): Promise<ILegacyCleoServerResponse<ILegacyDojoResponse<IPafAddress[]>>> {
    // https://cleo.sehnp.nhs.uk/SC/livecalld.nsf/agpostcode?Openagent&action=GET_POST_CODE_ADDRESSES&POST_CODE=SW192BP&dojo.preventCache=1637898378586
    const url =
      window.MyGlobalSession.Global_DB_Paths.HOST_PATH +
      "/" +
      window.MyGlobalSession.Global_DB_Paths.PATH_CALL +
      "/agpostcode?Openagent&action=GET_POST_CODE_ADDRESSES&POST_CODE=" +
      postCode;

    if (process.env.NODE_ENV === "development") {
      return Promise.resolve(pafMockData);
    } else {
      return window.localCache
        .getUrlDataWithCache(url, false, {})
        .then(resp => {
          return (resp as any) as ILegacyCleoServerResponse<
            ILegacyDojoResponse<IPafAddress[]>
          >;
        });
    }
  }
}
