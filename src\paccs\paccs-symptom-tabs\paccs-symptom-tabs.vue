<template>
  <div>
    <div
      v-for="(conditionTab, id) in conditionTabs"
      :key="id"
      class="paccs-symptom-tabs--symptom-link"
      :class="
        conditionTab.isSelected
          ? 'paccs-symptom-tabs--symptom-link-selected'
          : ''
      "
      v-on:click="tabSelected(conditionTab.templateId)"
    >
      <span v-text="conditionTab.tabName"></span>
      <a
        v-if="showCloseButton"
        href="#"
        v-on:click.prevent="tabDeSelected(conditionTab.templateId)"
        class="paccs-symptom-tabs--symptom-close-link"
      >
        X
      </a>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onBeforeMount,
  ref,
  SetupContext
} from "@vue/composition-api";
import { loggerInstance } from "@/common/Logger";
import { IConditionTab } from "@/paccs/paccs-symptom-tabs/paccs-symptom-tabs-models";

export default defineComponent({
  name: "paccs-symptom-tabs",
  props: {
    conditionTabs: {
      default: () => {
        return [];
      }
    },
    showCloseButton: {
      default: () => {
        return true;
      }
    }
  },
  components: {},
  setup(
    props: { conditionTabs: IConditionTab[]; showCloseButton: boolean },
    context: SetupContext
  ) {
    const currentlySelectedTab = ref("");

    onBeforeMount(() => {
      loggerInstance.log("paccs-symptom-tabs>>>>>>>>>>>>>>>>>>>mounted!");
    });

    const tabSelected = (id: string) => {
      currentlySelectedTab.value = id;
      context.emit("tabSelected", id);
    };

    const tabDeSelected = (id: string) => {
      context.emit("tabDeSelected", id);
    };

    const isTabSelected = (id: string) => {
      return id === currentlySelectedTab.value;
    };

    return {
      tabSelected,
      tabDeSelected,
      isTabSelected
    };
  }
});
</script>

<style>
.paccs-symptom-tabs--symptom-link {
  display: inline-block;
  margin: 0 0.5em;
  padding: 0.5em;
  background-color: #dedcdc;
  border-left: 1px solid grey;
  border-top: 1px solid grey;
  border-right: 1px solid grey;
  border-bottom: 1px solid lightgrey;
}

.paccs-symptom-tabs--symptom-link-selected {
  background-color: #9efba1;
  border-left: 1px solid #3e3efa;
  border-top: 1px solid #3e3efa;
  border-right: 1px solid #3e3efa;
  border-bottom: 1px solid white;
}
.paccs-symptom-tabs--symptom-close-link {
  font-weight: 600;
  text-decoration: none;
}
</style>
