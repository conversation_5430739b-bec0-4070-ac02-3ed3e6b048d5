## Preamble

This constitution establishes the immutable principles governing the development of CLEO, a Vue 2.7-based healthcare frontend application that serves as a modern interface for emergency medical services and healthcare call management. These principles ensure architectural consistency, maintainability, and quality across all specifications and implementations.

## Article I: Vue Component-First Principle

Every feature in CLEO MUST begin as a reusable Vue component—no exceptions. This forces modular design from the start:

```text
Every feature in CLEO MUST begin its existence as a standalone Vue component.
No feature shall be implemented directly within application code without
first being abstracted into a reusable component with clear props and events.
```

This principle ensures that specifications generate modular, reusable Vue components rather than monolithic application code. When generating implementation plans, features must be structured as components with clear boundaries and minimal dependencies.

## Article II: Props and Events Interface Mandate

Every Vue component MUST expose its functionality through a well-defined props/events interface:

```text
All component interfaces MUST:
- Accept data through props (reactive and typed)
- Emit events for user interactions and state changes
- Support v-model for two-way data binding where appropriate
- Document prop types and event payloads
```

This enforces observability and testability. Components cannot hide functionality inside opaque implementations—everything must be accessible and verifiable through the component interface.

## Article III: Test-First Imperative

The most transformative article—no component code before tests:

```text
This is NON-NEGOTIABLE: All component implementation MUST follow strict Test-Driven Development.
No component code shall be written before:
1. Unit tests are written for component logic
2. Component tests are written for user interactions
3. Tests are validated and approved by the user
4. Tests are confirmed to FAIL (Red phase)
```

This completely inverts traditional component development. Instead of generating components and hoping they work, specifications must first generate comprehensive tests that define behavior, get them approved, and only then generate implementation.

## Article IV: Vuex Store Module Principle

All shared state management MUST be implemented as Vuex store modules:

```text
State management in E4S MUST:
- Use Vuex 3 modules for shared state
- Follow the established store structure in store.ts
- Include proper typing with TypeScript interfaces
- Implement actions, mutations, and getters appropriately
```

This ensures consistent state management patterns across the application.

## Article V: Router Integration Principle

All navigation and route handling MUST follow Vue Router 3 patterns:

```text
Routing in CLEO MUST:
- Use named routes with meta information
- Implement title handling
- Follow the established router structure in router/router.ts
- Include proper route meta for titles
```

This maintains consistency in navigation and URL management.

## Article VI: Legacy Compatibility Principle

All new development MUST maintain compatibility with Vue 2.6 and existing composition-style components:

```text
Development in E4S MUST:
- Use Vue 2.6 compatible patterns
- Support existing vue-composition-component usage
- Avoid Vue 3-only features until migration is complete
- Maintain backwards compatibility with existing components
```

This ensures stability during the transition period.

## Article VII: Simplicity Gate

Implementation plans MUST pass simplicity validation:

```text
Section 7.1: Minimal Component Structure
- Maximum 3 new components for initial implementation
- Additional components require documented justification

Section 7.2: No Future-Proofing
- Implement only what's needed for current requirements
- Avoid speculative features or over-engineering

Section 7.3: Bundle Size Awareness
- Consider impact on Webpack bundle size
- Use dynamic imports for heavy features
```

## Article VIII: Framework Trust Principle

Use Vue 2.6 and ecosystem features directly rather than wrapping them:

```text
Section 8.1: Direct Framework Usage
- Use Vue directives, lifecycle hooks, and composition API directly
- Avoid unnecessary abstraction layers over Vue features

Section 8.2: Ecosystem Integration
- Use accessability where possible
- Use Vue i18n for internationalization

Section 8.3: Single Model Representation
- Use consistent data models across components
- Avoid multiple representations of the same data
```

## Article IX: Integration-First Testing

Prioritizes real-world testing over isolated unit tests:

```text
Tests MUST use realistic environments:
- Prefer component integration tests over isolated unit tests
- Use actual Vuex store instances over mocks
- Test complete user journeys end-to-end
- Contract tests mandatory for API interactions
```

## Article X: Performance and UX Principles

All implementations MUST consider performance and user experience:

```text
Section 10.1: Performance Awareness
- Implement lazy loading for heavy components
- Use computed properties efficiently
- Avoid unnecessary re-renders

Section 10.2: Accessibility
- Include proper ARIA labels and roles
- Support keyboard navigation
- Maintain legible component design

Section 10.3: Responsive Design
- Use responsive css
- Test on multiple screen sizes
- Follow established responsive patterns
```

## Constitutional Enforcement

### Phase -1: Pre-Implementation Gates

All implementation plans MUST pass these gates before proceeding:

#### Simplicity Gate (Article VII)
- [ ] Using ≤3 new components?
- [ ] No future-proofing or speculative features?
- [ ] Bundle size impact assessed?

#### Framework Trust Gate (Article VIII)
- [ ] Using Vue features directly?
- [ ] Single model representation?
- [ ] Ecosystem libraries used appropriately?

#### Integration-First Gate (Article IX)
- [ ] Component contracts defined?
- [ ] Integration tests planned?
- [ ] End-to-end scenarios identified?

#### Performance Gate (Article X)
- [ ] Lazy loading implemented where needed?
- [ ] Accessibility requirements met?
- [ ] Responsive design considered?

### Complexity Tracking

Any exceptions to these gates MUST be documented here with justification:

**Complexity Exceptions:**
- [Date] - [Exception] - [Justification] - [Approval]

## Amendment Process

Modifications to this constitution require:
- Explicit documentation of the rationale for change
- Review and approval by project maintainers
- Assessment of impact on existing specifications
- Backwards compatibility verification

## Effective Date

This constitution is effective as of [Current Date] and applies to all new specifications and implementations in the E4S project.