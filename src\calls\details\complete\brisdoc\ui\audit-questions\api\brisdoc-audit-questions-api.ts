import { getKeywordsStandard } from "@/common/api/keywords-api";
import {
  CALL_CLASSIFICATION,
  CLEO_CLIENT_SERVICE
} from "@/common/common-models";
import { Question } from "@/common/ui/dynamic-question/models/dynamic-question-models";
import { mockAuditQuestions } from "@/calls/details/complete/brisdoc/ui/audit-questions/mock/mockAuditQuestions";

/**
 *
 * @param cleoClientService
 * @param classification
 * @param service
 *
 * https://ash-brisdoc-cleouat.sehnp.nhs.uk/dev/
 * livexcleolock.nsf/xpbeaninterface.xsp?processformat=json&action=GETKEYWORDSTANDARDJSON&sid=AUDIT_QUESTION_DATA_MENTAL_HEALTH~BrisDoc
 * E.g. service = "BrisDoc"
 * {
 *   "TRIAGE_TYPE": {
 *     "description": "AUDIT_QUESTION_DATA_MENTAL_HEALTH",
 *     "KeywordService": "BrisDoc;CAS",
 *     "codeID1": "{....json data....}",
 *   }
 * }
 */
export function getAuditQuestions(
  cleoClientService: CLEO_CLIENT_SERVICE,
  classification: CALL_CLASSIFICATION,
  service?: string
): Promise<Question[]> {
  const serverKeyword = "AUDIT_QUESTION_DATA";
  let keywordLookup = "";
  let keywordLookupOther = "";

  if (classification === "Advice") {
    keywordLookup = serverKeyword + "_" + cleoClientService;
    keywordLookupOther = serverKeyword + "_OTHER";
  } else {
    keywordLookup = serverKeyword + "_CLASS_" + classification.toUpperCase();
  }

  const urlParams = {
    action: "GETKEYWORDSTANDARDJSON",
    sid: serverKeyword + (service && service.length > 0 ? "~" + service : "")
  };

  if (process.env.NODE_ENV === "development") {
    return new Promise(resolve => {
      setTimeout(() => {
        const resp = mockAuditQuestions;

        const data = resp[keywordLookup] || resp[keywordLookupOther];
        const codeID1: string = data.codeID1 as string;

        resolve(JSON.parse(codeID1));
      }, 2000);
    });
  }

  return getKeywordsStandard(urlParams).then(resp => {
    //  If string then convert to Json object
    resp = typeof resp === "string" ? JSON.parse(resp) : resp;

    const data = resp[keywordLookup] || resp[keywordLookupOther];
    if (data && data.codeID1) {
      return JSON.parse(data.codeID1);
    }
    return [];
  });
}
