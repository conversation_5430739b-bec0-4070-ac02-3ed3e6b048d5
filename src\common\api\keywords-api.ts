import { LegacyKeywordServerResponse } from "@/common/cleo-legacy-models";
import https from "@/common/https";
import { CLEO_CONFIG } from "@/common/config/config-";

/**
 *
 * @param urlParams  e.g.
 * {
 *  action: "GETKEYWORDSTANDARDJSON",
 *  sid: "OVERSIGHT_BASE_QUESTION__TRIAGE_TYPE~" + (service.length > 0 ? "~" + service : "")
 * }
 */
export function getKeywordsStandard(
  urlParams: Record<string, string>
): Promise<LegacyKeywordServerResponse> {
  console.warn("FollowUpApi.getQuestion() urlParams", urlParams);

  return (https
    .get(
      CLEO_CONFIG.CLEO.XCLEO_PATH + "/xpbeaninterface.xsp?processformat=json",
      {
        responseType: "json",
        params: urlParams
      }
    )
    .then(resp => {
      console.log("keywords-api resp type:", typeof resp, "value:", resp);
      //  If string then convert to Json object
      resp = typeof resp === "string" ? JSON.parse(resp) : resp;
      console.log("keywords-api after parse:", resp);
      return resp;
    }) as any) as Promise<LegacyKeywordServerResponse>;
}
