<template>
  <div>
    <div class="what-3-words-cleo">
      <div class="what-3-words-cleo--input-section">
        <div>
          <span class="what-3-words-cleo--search-icons">
            <img
              v-on:click.prevent="
                what3WordsController.switchAutoSuggestSearchType()
              "
              src="../assets/icons8-google-maps-48.png"
              class="what-3-words-cleo--icon-google"
              :class="
                what3WordsController.state.userInput.autoSuggestSearchType ===
                'w3w'
                  ? 'what-3-words-cleo--icon-search-type-inactive'
                  : ''
              "
            />
            <img
              v-on:click.prevent="
                what3WordsController.switchAutoSuggestSearchType()
              "
              src="../assets/w3w_AlignedLogo_RGB_RedWhite_ENG.png"
              class="what-3-words-cleo--icon-w3w"
              :class="
                what3WordsController.state.userInput.autoSuggestSearchType ===
                'google'
                  ? 'what-3-words-cleo--icon-search-type-inactive'
                  : ''
              "
            />
          </span>

          <What3WordsCleoAutoSuggest
            v-if="
              what3WordsController.state.userInput.autoSuggestSearchType ===
                'w3w'
            "
            class="what-3-words-cleo--auto-suggest-picker"
            :auto-suggest="what3WordsController.state.userInput.autoSuggest"
            :what3-words-suggestions="
              what3WordsController.currentSuggestions.value
            "
            v-on:findSuggestions="what3WordsController.autoSuggest"
            v-on:clearUi="what3WordsController.autoSuggestClear"
            v-on:suggestionSelected="what3WordsController.suggestionSelected"
          />

          <GoogleAutoPredictions
            v-if="
              what3WordsController.state.userInput.autoSuggestSearchType ===
                'google'
            "
            class="what-3-words-cleo--auto-suggest-picker"
            :auto-suggest="
              what3WordsController.state.userInput.googleAutoPredictionQuery
            "
            :predictions="
              what3WordsController.state.results.googleAutoPredictions
            "
            v-on:findSuggestions="what3WordsController.getQueryPredictions"
            v-on:suggestionSelected="
              what3WordsController.autoQueryPredictionSelected
            "
          />

          <div v-if="false" class="what-3-words-cleo--paf-picker">
            <What3WordsCleoPafPicker
              :post-code="what3WordsController.state.userInput.pafPostCode"
              v-on:addressSelected="what3WordsController.pafAddressSelected"
            />
          </div>

          <div class="what-3-words-cleo--sms-section">
            <input
              class="what-3-words-cleo-paf-picker--sms-input"
              :class="
                what3WordsController.isValidPhoneNumber.value
                  ? ''
                  : 'what-3-words-cleo--input-invalid'
              "
              v-model="what3WordsController.state.userInput.mobileNumber"
              placeholder="SMS number..."
            />
            <div class="adapter-button--separator"></div>
            <button
              class="adapter-button adapter-width-8 adapter-button--green"
              :disabled="what3WordsController.isSmsButtonDisabled.value"
              v-on:click="what3WordsController.sendSms()"
            >
              Send FindMe SMS
            </button>

            Sent:
            <span v-text="what3WordsController.state.results.smsCount"></span>
          </div>

          <div class="what-3-words-cleo--buttons">
            <div class="adapter-button--separator"></div>
            <button
              class="adapter-button adapter-width-5 adapter-button--green"
              v-on:click="what3WordsController.confirm()"
            >
              Confirm
            </button>
            <div class="adapter-button--separator"></div>
            <button
              class="adapter-button adapter-width-5 adapter-button--red"
              v-on:click="what3WordsController.close()"
            >
              Cancel
            </button>
          </div>
        </div>

        <!--        <div v-if="!what3wordsControllerState.map.isZoomOkForOverlay">-->
        <!--          &lt;!&ndash;          IsZoomOk: {{ what3wordsControllerState.map.isZoomOkForOverlay }}&ndash;&gt;-->
        <!--          What3Words requires you are zoomed in closer on the map to display the-->
        <!--          overlay grid.-->
        <!--        </div>-->
      </div>

      <div class="what-3-words-cleo--map-section">
        <div id="map-cleo" style="height: 100%;width: 100%;"></div>
      </div>

      <div v-if="false" class="what-3-words-cleo--right-section">
        <div>
          <button v-on:click="what3WordsController.close()">Close</button>
          <button v-on:click="what3WordsController.wordsConfirmed()">
            Words
          </button>
          <button v-on:click="what3WordsController.pafAddressConfirmed()">
            Address
          </button>
          Click postcode for PAF search:
          {{
            what3WordsController.state.results.pafSelected
              ? what3WordsController.state.results.pafSelected.GPS
              : ""
          }}

          <div
            v-for="postCode in what3WordsController.state.results
              .geocoderResultsPostCodes"
            :key="postCode"
          >
            <a
              href="#"
              v-on:click.prevent="what3WordsController.searchPaf(postCode)"
            >
              <span v-text="postCode"></span>
            </a>
          </div>
        </div>

        <What3WordsCleoConfirmed
          v-if="false"
          :what3words-controller-state="what3WordsController.state"
          v-on:toggleWhat3WordsConfirmed="
            what3WordsController.toggleWhat3WordsConfirmed()
          "
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onMounted,
  PropType,
  SetupContext,
  toRaw,
  watch
} from "@vue/composition-api";
import { useWhat3WordsController } from "@/what3words/useWhat3WordsController";
import { IWhat3wordsControllerState } from "@/what3words/what3words-cleo-models";
import { What3wordsCleoService } from "@/what3words/what3words-cleo-service";
import {
  getMapControllerInstance,
  IMapController
} from "@/calls/maps/map-controller-factory";
import { Loader } from "@googlemaps/js-api-loader";
import What3WordsCleoAutoSuggest from "@/what3words/What3WordsCleoAutoSuggest.vue";
import What3WordsCleoPafPicker from "@/what3words/What3WordsCleoPafPicker.vue";
import What3WordsCleoConfirmed from "@/what3words/What3WordsCleoConfirmed.vue";
import GoogleAutoPredictions from "@/calls/maps/addresses/GoogleAutoPredictions.vue";
import { useVehicleController } from "@/vehicles/useVehicleController";
import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import { ICallDetail } from "@/calls/details/call-details-models";
import { CallSummaryService } from "@/calls/summary/call-summary-service";
import * as CallDetailService from "@/calls/details/call-detail-service";
import { loggerInstance } from "@/common/Logger";
import { CommonService } from "@/common/common-service";

// "esm": "^3.2.25",
//   "geodesy": "^2.2.1",
// "@types/geodesy": "^2.2.2",
// import Utm, { LatLon } from "geodesy/utm";
// import OsGridRef from "geodesy/osgridref";

const what3wordsCleoService = new What3wordsCleoService();
const callSummaryService = new CallSummaryService();
// const callDetailService = new CallDetailService();

declare const google: any;

export default defineComponent({
  name: "what-3-words-cleo",
  props: {
    what3wordsControllerState: {
      type: Object as PropType<IWhat3wordsControllerState>,
      default: () => {
        return what3wordsCleoService.factoryWhat3wordsControllerState();
      }
    },
    cleoCallSummary: {
      type: Object as PropType<ICleoCallSummary>,
      default: () => {
        return callSummaryService.factoryCleoCallSummary();
      }
    },
    callDetail: {
      type: Object as PropType<ICleoCallSummary>,
      default: () => {
        return CallDetailService.factoryCallDetail();
      }
    }
  },
  components: {
    GoogleAutoPredictions,
    What3WordsCleoConfirmed,
    What3WordsCleoPafPicker,
    // PafUserPicker,
    What3WordsCleoAutoSuggest
  },
  setup(
    props: {
      apiKey: string;
      what3wordsControllerState: IWhat3wordsControllerState;
      cleoCallSummary: ICleoCallSummary;
      callDetail: ICallDetail;
    },
    context: SetupContext
  ) {
    const what3WordsController = useWhat3WordsController(
      props.what3wordsControllerState,
      context
    );

    let mapController: IMapController = getMapControllerInstance();
    const vehicleController = useVehicleController();
    const commonService = new CommonService();

    /*
    //50.74166213645987, -3.072222282803345
    const p1 = new LatLon(50.74166213645987, -3.072222282803345);
    // const utm = Utm.parse(p1.toUtm().)
    const d = p1.toUtm();
    console.log("================d: ", d);
    console.log("================d: ", d.toLatLon());
    //
    // const os = new OsGridRef(d.easting, d.northing);
    */

    watch(
      () => props.callDetail,
      (newValue: ICallDetail) => {
        if (newValue.CallNo > 0) {
          loggerInstance.log(
            "What3WordsCleo watch callDetail, move map to the call position"
          );
          dropMarkerForCurrentCallLocation(newValue);
        }
      }
    );

    watch(
      () => props.cleoCallSummary,
      (newValue: ICleoCallSummary) => {
        if (newValue.CallNo > 0) {
          loggerInstance.log(
            "What3WordsCleo watch cleoCallSummary, move map to the call position"
          );
          dropMarkerForCurrentCallLocation(newValue);
        }
      }
    );

    function dropMarkerForCurrentCallLocation(
      callData: ICallDetail | ICleoCallSummary
    ) {
      vehicleController.initDispatch().then(() => {
        what3WordsController.state.results.vehicles = commonService.simpleObjectClone(
          toRaw(vehicleController.vehicles.value)
        );

        what3WordsController.dropMarkerForCurrentCallLocation(
          callData,
          toRaw(vehicleController.vehicles.value)
        );
      });
    }

    onMounted(() => {
      let loader = new Loader({
        apiKey: props.what3wordsControllerState.map.apiKey,
        libraries: ["geometry", "places"]
      });
      loader.load().then(() => {
        const mapDiv = document.getElementById("map-cleo");
        if (mapDiv) {
          const what3WordsMap = new google.maps.Map(mapDiv, {
            center: {
              lat: 51.43333,
              lng: 0.55
            },
            zoom: 18,
            zoomControl: true,
            scaleControl: true
          });

          mapController.setGoogleMap(what3WordsMap);
          what3WordsController.setMap(mapController);
          window.setTimeout(() => {
            what3WordsController.init();

            //  Call been passed in, move map to that location
            if (props.callDetail.CallNo > 0) {
              dropMarkerForCurrentCallLocation(props.callDetail);
            } else if (props.cleoCallSummary.CallNo > 0) {
              dropMarkerForCurrentCallLocation(props.cleoCallSummary);
            }
          }, 500);
        }
      });
    });

    return {
      what3WordsController,
      mapController,
      vehicleController
    };
  }
});
</script>

<style>
.what-3-words-cleo {
  height: 100%;
  width: 100%;
}

.what-3-words-cleo--input-section {
  height: 40px;
  width: 100%;
}
.what-3-words-cleo--map-section {
  height: 85vh;
  width: 100%;
  display: inline-block;
}
.what-3-words-cleo--right-section {
  height: 80vh;
  width: 20vw;
  /*background-color: #3a66dd;*/
  display: inline-block;
  vertical-align: top;
}

.what-3-words-cleo--auto-suggest-picker {
  position: relative;
  background-color: white;
  /*top: 5px;*/
  left: 5px;
  z-index: 15;
  width: 400px;
  display: inline-block;
  vertical-align: top;
}

.what-3-words-cleo--paf-picker {
  position: relative;
  background-color: white;
  /*top: 5px;*/
  left: 5px;
  z-index: 15;
  width: 400px;
  display: inline-block;
  margin-left: 10px;
  vertical-align: top;
}

.what-3-words-cleo--sms-section {
  position: relative;
  /*top: 5px;*/
  left: 5px;
  z-index: 15;
  /*width: 400px;*/
  display: inline-block;
  margin-left: 10px;
  vertical-align: top;
}

.what-3-words-cleo--buttons {
  /*position: relative;*/
  /*left: 400px;*/
  /*top: 5px;*/
  /*display: inline-block;*/
  float: right;
}

.what-3-words-cleo-paf-picker--sms-input {
  line-height: 2;
  width: 100px;
  font-weight: 600;
}

.what-3-words-cleo--google-auto-suggest-picker {
  position: relative;
  background-color: white;
  top: 100px;
  left: 5px;
  z-index: 15;
  width: 400px;
  display: inline-block;
  vertical-align: top;
}

.what-3-words-cleo--input-invalid {
  background-color: #f3b5b5;
}
.what-3-words-cleo--search-icons {
  margin-left: 5px;
}

.what-3-words-cleo--icon-google {
  height: 35px;
  vertical-align: top;
  margin-top: -2px;
}
.what-3-words-cleo--icon-w3w {
  height: 50px;
  vertical-align: top;
  margin-top: -5px;
}

.what-3-words-cleo--icon-search-type-inactive {
  opacity: 0.5;
}
</style>
