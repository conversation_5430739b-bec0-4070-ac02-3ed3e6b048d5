<template>
  <div class="ic24-flex-column ic24-flex-gap">
    <complete-step-header :step="step" />

    <div class="ic24-vertical-spacer-large"></div>

    <div
      class="ic24-flex-row ic24-justify-fle-row-vert-center vulnerability--question"
    >
      <span class="vulnerability--label">Have you made a child protection safeguarding referral?</span>

      <div class="ic24-flex-row ic24-flex-gap-large ic24-flex-row--end vulnerability--radio-group">
        <RadioButtonObj
          v-model="valueInternal.child"
          :option-value="options.YES"
          :label="options.YES.description"
          @onChanged="onSelected"
        />

        <RadioButtonObj
          v-model="valueInternal.child"
          :option-value="options.NO"
          :label="options.NO.description"
          @onChanged="onSelected"
        />
      </div>
    </div>

    <div
      class="ic24-flex-row ic24-justify-fle-row-vert-center vulnerability--question"
    >
      <span class="vulnerability--label">Have you made an adult safeguarding referral?</span>

      <div class="ic24-flex-row ic24-flex-gap-large ic24-flex-row--end vulnerability--radio-group">
        <RadioButtonObj
          v-model="valueInternal.adult"
          :option-value="options.YES"
          :label="options.YES.description"
          @onChanged="onSelected"
        />

        <RadioButtonObj
          v-model="valueInternal.adult"
          :option-value="options.NO"
          :label="options.NO.description"
          @onChanged="onSelected"
        />
      </div>
    </div>

    <div
      class="ic24-flex-row ic24-justify-fle-row-vert-center vulnerability--question"
    >
      <span class="vulnerability--label"
        >Does this patient require Mental Capacity Assessment to support the management plan?</span
      >

      <div class="ic24-flex-row ic24-flex-gap-large ic24-flex-row--end vulnerability--radio-group">
        <RadioButtonObj
          v-model="valueInternal.mcaRequired"
          :option-value="options.YES"
          :label="options.YES.description"
          @onChanged="onSelected"
        />

        <RadioButtonObj
          v-model="valueInternal.mcaRequired"
          :option-value="options.NO"
          :label="options.NO.description"
          @onChanged="onSelected"
        />
      </div>
    </div>

    <div
      v-if="valueInternal.mcaRequired.value === 'YES'"
      class="ic24-flex-row ic24-justify-fle-row-vert-center vulnerability--question"
    >
      <span class="vulnerability--label"
        >Has it been assessed, as recommended by the MCA 2005?</span
      >

      <div class="ic24-flex-row ic24-flex-gap-large ic24-flex-row--end vulnerability--radio-group">
        <RadioButtonObj
          v-model="valueInternal.mcaAssessed"
          :option-value="options.YES"
          :label="options.YES.description"
          @onChanged="onSelected"
        />

        <RadioButtonObj
          v-model="valueInternal.mcaAssessed"
          :option-value="options.NO"
          :label="options.NO.description"
          @onChanged="onSelected"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  SetupContext
} from "@vue/composition-api";
import { IStep, IVulnerability } from "../../complete-models";
import { simpleObjectClone } from "@/common/common-utils";
import CompleteStepHeader from "@/calls/details/complete/CompleteStepHeader.vue";
import RadioButtonObj from "@/common/ui/fields/RadioButtonObj.vue";

export default defineComponent({
  name: "Vulnerability",
  components: { RadioButtonObj, CompleteStepHeader },
  props: {
    step: {
      type: Object as PropType<IStep<"VULNERABILITY">>,
      required: true
    },
    value: {
      type: Object as PropType<IVulnerability>,
      required: true
    }
  },
  setup(
    props: {
      step: IStep<"VULNERABILITY">;
      value: IVulnerability;
    },
    context: SetupContext
  ) {
    const valueInternal = ref<IVulnerability>(simpleObjectClone(props.value));

    const options = {
      YES: {
        id: "YES",
        description: "Yes",
        value: "YES"
      },
      NO: {
        id: "NO",
        description: "No",
        value: "NO"
      }
    };

    function onSelected() {
      console.log("Vulnerability.onSelected: ", valueInternal.value);
      context.emit("input", simpleObjectClone(valueInternal.value));
    }

    return { valueInternal, onSelected, options };
  }
});
</script>

<style scoped>
.vulnerability--question {
  max-width: 500px;
}
.vulnerability--label {
  width: 360px;
  padding-right: var(ic24-flex-gap);
}
.vulnerability--radio-group {
  width: 135px;
}
</style>
