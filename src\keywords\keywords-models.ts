import { IBase } from "@/common/common-models";

export interface ICleoBase extends IBase {
  Name: string;
}

export interface IClassification extends IBase {
  Description: string;
}

export interface IAcvpuType extends IBase {
  TypeName: string;
}

export interface IPatientRepresentative extends IBase {
  Representative: string;
}

export interface ITemperatureScale extends IBase {
  ScaleName: string;
}

export interface IClientDevice extends IBase {
  DeviceName: string;
  MacAddress: string;
  MachineSid: string;
  Active: boolean;
}

export interface IFailedContactCode extends IBase {
  Message: string;
}

//  Here as example to type check roles...but we should not code to roles
//  but rather permissions.
export type CLEO_ROLE =
  | "[Role Base Recep]"
  | "[Role Call Hand]"
  | "[Role CH 111]";

export interface ICleoRole {
  Id: number;
  Name: string;
  LegacyName: string | CLEO_ROLE;
}

export interface IKeywordsRefData {
  bases?: ICleoBase[];
  classifications?: IClassification[];
  roles?: ICleoRole[];
  acvputypes?: IAcvpuType[];
  patientrepresentatives?: IPatientRepresentative[];
  temperaturescaletypes?: ITemperatureScale[];
  clientdevices?: IClientDevice[];
  failedcontactcodes?: IFailedContactCode[];
}

export interface IKeywordsRefDataKey {
  bases?: Record<string, ICleoBase>;
  classifications?: Record<string, IClassification>;
  roles?: Record<string, ICleoRole>;
  acvputypes?: Record<string, IAcvpuType>;
  patientrepresentatives?: Record<string, IPatientRepresentative>;
  temperaturescaletypes?: Record<string, ITemperatureScale>;
  clientdevices?: Record<string, IClientDevice>;
  failedcontactcodes?: Record<string, IFailedContactCode>;
}
