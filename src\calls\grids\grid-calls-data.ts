import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
// import { ResourceData } from "@/common/resource/resource-service";
import { CLEO_CONFIG } from "@/common/config/config-";
import https from "@/common/https";
import { IAdapterPagedResponse } from "@/common/common-models";

export interface IAdapterSearchCriteria {
  CallClassificationId?: number;
  serviceId?: number;
}

export interface IAdapterQueueParams {
  PageNumber: number;
  RecordsPerPage: number;
  QueryName: string;
  Criteria: IAdapterSearchCriteria;
}

export class GridCallsData {
  private adapterEndPoint = CLEO_CONFIG.ADAPTER_URL
    ? CLEO_CONFIG.ADAPTER_URL
    : "";

  public getQueueApi(
    adapterQueueParams: IAdapterQueueParams
  ): Promise<IAdapterPagedResponse<ICleoCallSummary>> {
    if (
      !(adapterQueueParams.QueryName && adapterQueueParams.QueryName.length > 0)
    ) {
      throw Error("GridCallsData.getQueueApi() No Query Name passed.");
    }

    return https.post(
      this.adapterEndPoint + "/api/calls/searchCalls",
      adapterQueueParams,
      {
        responseType: "json"
      }
    );
  }

  public getQueueApiFromCleo(
    viewName: string,
    key?: string,
    pageNumber?: number,
    pageSize?: number
  ): Promise<IAdapterPagedResponse<ICleoCallSummary>> {
    // https://cleo.staging.sehnp.nhs.uk/stage/calld.nsf/getViewData?openagent&vn=CLERIC_ADAPTER&PAGE_ON=1&PAGE_NUM=1&dojo.preventCache=1581334540506
    return https.get(
      CLEO_CONFIG.CLEO.CALL_DB + "/getViewData?openagent&vn=" + viewName,
      {
        responseType: "json"
      }
    );
  }
}
