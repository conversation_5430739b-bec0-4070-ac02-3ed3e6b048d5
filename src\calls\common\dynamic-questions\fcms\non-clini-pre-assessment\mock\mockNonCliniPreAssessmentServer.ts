export const mockNonCliniPreAssessmentServer = {
  NON_CLINI_PRE_ASSESSMENT_FCMS: {
    unid: "FCMS001234567890ABCDEF123456789012",
    codeID3: "",
    description: "NON_CLINI_PRE_ASSESSMENT_FCMS",
    KeywordService: "FCMS",
    codeID2: "",
    codeID1: JSON.stringify([
      {
        id: "withPatient",
        type: "radio",
        label: "Q1: Are you with the patient?",
        options: ["Yes", "No"],
        mandatory: true,
        value: null,
        visible: true
      },
      {
        id: "canContactPatientDirectly",
        type: "radio",
        label: "Q2: Can we contact the patient directly?",
        options: ["Yes", "No"],
        mandatory: true,
        value: null,
        visible: false,
        condition: "answers['withPatient'] === 'No'",
        helpText:
          "Keep the caller on the line until successful contact with the patient has been made – if successful contact cannot be made, select No to this question."
      },
      {
        id: "callerIsClinician",
        type: "radio",
        label: "Q3: Is the caller a Clinician?",
        options: ["Yes", "No"],
        mandatory: true,
        value: null,
        visible: false,
        condition:
          "(answers['withPatient'] === 'Yes') || (answers['withPatient'] === 'No' && answers['canContactPatientDirectly'] === 'No')"
      },
      {
        id: "patientEndOfLifeCare",
        type: "radio",
        label: "Q4: Is the patient on end-of-life care?",
        options: ["Yes", "No"],
        mandatory: true,
        value: null,
        visible: false,
        condition: "answers['callerIsClinician'] === 'No'",
        helpText:
          "If Yes: Please follow the local policy for managing patients who are receiving end of life care"
      },
      {
        id: "patientBreathingConscious",
        type: "radio",
        label: "Q5: Is the patient breathing and conscious?",
        options: ["Yes", "No"],
        mandatory: true,
        value: null,
        visible: false,
        condition: "answers['patientEndOfLifeCare'] === 'No'"
      },
      {
        id: "patientCall999",
        type: "select",
        label: "Q6: Is the Patient/Caller happy to dial 999?",
        options: [
          "",
          "Patient/Caller WILL call 999",
          "Patient/Caller DECLINED 999"
        ],
        mandatory: true,
        value: "",
        visible: false,
        condition:
          "answers['patientBreathingConscious'] === 'No' || answers['majorBloodLoss'] === 'Yes' || (answers['tooBreathless'] === 'Yes' && answers['normallyBreathless'] === 'No') || answers['crushingChestPain'] === 'Yes' || answers['strokeSymptoms'] === 'Yes'"
      },
      {
        id: "majorBloodLoss",
        type: "radio",
        label:
          "Q7: Has the patient had any major blood loss in the last 30 minutes?",
        options: ["Yes", "No"],
        mandatory: true,
        value: null,
        visible: false,
        condition: "answers['patientBreathingConscious'] === 'Yes'"
      },
      {
        id: "tooBreathless",
        type: "radio",
        label:
          "Q8: Is the patient too breathless to speak in complete sentences?",
        options: ["Yes", "No"],
        mandatory: true,
        value: null,
        visible: false,
        condition: "answers['majorBloodLoss'] === 'No'"
      },
      {
        id: "normallyBreathless",
        type: "radio",
        label: "Q8a: Is the patient normally this breathless?",
        options: ["Yes", "No"],
        mandatory: true,
        value: null,
        visible: false,
        condition: "answers['tooBreathless'] === 'Yes'"
      },
      {
        id: "chestPain",
        type: "radio",
        label: "Q9: Has the caller described any chest pain?",
        options: ["Yes", "No"],
        mandatory: true,
        value: null,
        visible: false,
        condition:
          "(answers['tooBreathless'] === 'No') || (answers['tooBreathless'] === 'Yes' && answers['normallyBreathless'] === 'Yes')"
      },
      {
        id: "crushingChestPain",
        type: "radio",
        label:
          "Q10: Has the caller described the chest pain as a crushing or severe aching pain radiating to the arm, neck, jaw or shoulder?",
        options: ["Yes", "No"],
        mandatory: true,
        value: null,
        visible: false,
        condition: "answers['chestPain'] === 'Yes'"
      },
      {
        id: "strokeSymptoms",
        type: "radio",
        label: "Q11: Has the caller described any stroke-like symptoms?",
        options: ["Yes", "No"],
        mandatory: true,
        value: null,
        visible: false,
        condition:
          "(answers['chestPain'] === 'No') || (answers['chestPain'] === 'Yes' && answers['crushingChestPain'] === 'No')",
        helpText: "FAST TEST – FACE, ARM, SPEECH, TIME"
      },
      {
        id: "selectPriority",
        type: "select",
        label: "Q12: Please select Priority",
        options: [
          "",
          "20 minutes",
          "30 minutes",
          "1 hour",
          "2 hours",
          "4 hours",
          "6 hours",
          "12 hours"
        ],
        mandatory: true,
        value: "",
        visible: false,
        condition:
          "answers['callerIsClinician'] === 'Yes' || (answers['strokeSymptoms'] === 'No')"
      }
    ]),
    keyType: "NON_CLINI_PRE_ASSESSMENT"
  }
};
