import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import { CallSummaryService } from "@/calls/summary/call-summary-service";

describe("GridDefault Column Definitions CallNo === 0 Check", () => {
  let callSummaryService: CallSummaryService;
  let mockCallSummaryWithZeroCallNo: ICleoCallSummary;
  let mockCallSummaryWithValidCallNo: ICleoCallSummary;

  beforeEach(() => {
    callSummaryService = new CallSummaryService();

    // Create mock call summary with CallNo = 0
    mockCallSummaryWithZeroCallNo = callSummaryService.factoryCleoCallSummary();
    mockCallSummaryWithZeroCallNo.CallNo = 0;

    // Create mock call summary with valid CallNo
    mockCallSummaryWithValidCallNo = callSummaryService.factoryCleoCallSummary();
    mockCallSummaryWithValidCallNo.CallNo = 12345;
    mockCallSummaryWithValidCallNo.CallFAction = "Test Action";
    mockCallSummaryWithValidCallNo.CallNhsNo = "**********";
    mockCallSummaryWithValidCallNo.CallAge = 25;
    mockCallSummaryWithValidCallNo.CallAgeClass = "Y";
    mockCallSummaryWithValidCallNo.CallTown = "Test Town";
    mockCallSummaryWithValidCallNo.PLS_REASON = "Test PLS Reason";
    mockCallSummaryWithValidCallNo.PLS_ACTIONTEXT = "Test PLS Action";
  });

  describe("CallNo === 0 Check Logic", () => {
    it("should return empty string when CallNo is 0", () => {
      // Test the core logic that should be in all column definitions
      function testValueGetter(cleoCallSummary: ICleoCallSummary): string {
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return cleoCallSummary.CallFAction || "";
      }

      const result = testValueGetter(mockCallSummaryWithZeroCallNo);
      expect(result).toBe("");
    });

    it("should return actual value when CallNo is not 0", () => {
      function testValueGetter(cleoCallSummary: ICleoCallSummary): string {
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return cleoCallSummary.CallFAction || "";
      }

      const result = testValueGetter(mockCallSummaryWithValidCallNo);
      expect(result).toBe("Test Action");
    });

    it("should handle null data gracefully", () => {
      function testValueGetter(
        cleoCallSummary: ICleoCallSummary | null
      ): string {
        if (!cleoCallSummary || cleoCallSummary.CallNo === 0) {
          return "";
        }
        return cleoCallSummary.CallFAction || "";
      }

      const result = testValueGetter(null);
      expect(result).toBe("");
    });

    it("should handle undefined data gracefully", () => {
      function testValueGetter(
        cleoCallSummary: ICleoCallSummary | undefined
      ): string {
        if (!cleoCallSummary || cleoCallSummary.CallNo === 0) {
          return "";
        }
        return cleoCallSummary.CallFAction || "";
      }

      const result = testValueGetter(undefined);
      expect(result).toBe("");
    });

    it("should handle missing CallNo property gracefully", () => {
      function testValueGetter(cleoCallSummary: any): string {
        if (!cleoCallSummary || cleoCallSummary.CallNo === 0) {
          return "";
        }
        return cleoCallSummary.CallFAction || "";
      }

      const mockDataWithoutCallNo = {};
      const result = testValueGetter(mockDataWithoutCallNo);
      expect(result).toBe("");
    });
  });

  describe("Additional Test Cases", () => {
    it("should test different data types for CallNo", () => {
      function testValueGetter(cleoCallSummary: any): string {
        if (!cleoCallSummary || cleoCallSummary.CallNo === 0) {
          return "";
        }
        return cleoCallSummary.CallFAction || "";
      }

      // Test with string "0"
      const mockWithStringZero = { ...mockCallSummaryWithValidCallNo };
      mockWithStringZero.CallNo = "0" as any;
      const result = testValueGetter(mockWithStringZero);
      expect(result).toBe("Test Action"); // Should not match since "0" !== 0

      // Test with actual number 0
      const result2 = testValueGetter(mockCallSummaryWithZeroCallNo);
      expect(result2).toBe("");

      // Test with negative number
      const mockWithNegative = { ...mockCallSummaryWithValidCallNo };
      mockWithNegative.CallNo = -1;
      const result3 = testValueGetter(mockWithNegative);
      expect(result3).toBe("Test Action");
    });

    it("should test cellRenderer logic pattern", () => {
      function testCellRenderer(cleoCallSummary: ICleoCallSummary): string {
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return `<span>CallNo: ${cleoCallSummary.CallNo}</span>`;
      }

      const result1 = testCellRenderer(mockCallSummaryWithZeroCallNo);
      expect(result1).toBe("");

      const result2 = testCellRenderer(mockCallSummaryWithValidCallNo);
      expect(result2).toBe("<span>CallNo: 12345</span>");
    });

    it("should test tooltipValueGetter logic pattern", () => {
      function testTooltipValueGetter(
        cleoCallSummary: ICleoCallSummary
      ): string {
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return `Tooltip for CallNo: ${cleoCallSummary.CallNo}`;
      }

      const result1 = testTooltipValueGetter(mockCallSummaryWithZeroCallNo);
      expect(result1).toBe("");

      const result2 = testTooltipValueGetter(mockCallSummaryWithValidCallNo);
      expect(result2).toBe("Tooltip for CallNo: 12345");
    });
  });
});
