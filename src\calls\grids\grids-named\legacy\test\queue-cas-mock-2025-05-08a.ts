import { GridLegacyServerResponse } from "@/calls/grids/grids-named/legacy/models/grid-legacy-models";

export const queueCasMockSource20250508a: GridLegacyServerResponse = ({
  Count: 38,
  Returned: 38,
  identifier: "unid",
  label: "name",
  Limit: 500,
  items: [
    {
      unid: "4CA45D22806434ED80258CEA00352BE3",
      name: "250694094",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250694094",
      CallID: "250694094",
      CallNHSNo: "",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "BrisDoc",
      CallServiceAlt: "",
      CallMF: "Male",
      Call_HCP: "0",
      CallDobIso: "1996-03-25",
      CallPatientTitle: "",
      CallAddress1: "313 VICTORIA ROAD",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "LOWESTOFT",
      CallPostCode: "NR33 9LS",
      UTC_Assigned: "",
      GOODSAM_IMAGE_STATUS: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "Ben Smythson",
      CallCName: "TEST",
      CallCRel: "111 referral via fax/phone/email",
      BreachKey: "AdviceNo",
      ApplyBreach: "1",
      CallReceivedISO: "2025-08-18T10:43:30+01:00",
      CallReceivedTimeISO: "2025-08-18T10:40:46+01:00",
      BreachWarnActualTime: "2025-08-19T10:03:30+01:00",
      BreachPreActualTime: "2025-08-18T10:43:30+01:00",
      BreachActualTime: "2025-08-19T10:43:30+01:00",
      BreachPriority: "11",
      BreachLevel1Mins: "1440",
      Source: "",
      BreachPriorityGroup: "Speak To",
      BreachPriorityLabel: "Less Urgent 24 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "10:40",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "29 yrs",
      CallDoctorNameCN: "",
      PatientName: "BEN HTML TEST, Test",
      CallTriaged: "No",
      CallSymptoms:
        "Symptoms Entered to Capture: HTML Tags[BR]This text has been entered onto the next line so should not display a line break.[BR]This is another new line, and contains a few examples of special characters : ? [FWDSLASH] > . < [BACKSLASH]",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "0",
      CallTelNo: "01233 123123",
      CallTelNo_R: "01233 123123",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "BEN HTML TEST",
      CallForename: "Test",
      CallDoctorName: "",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPractice: "A TEST PRACTICE",
      CallPracticeOCS: "**********",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "Dx15",
      CHFinalDispositionDescription:
        "Speak to a Primary Care Service within 24 hours",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      FLAG_REMOVE_FIRST_CONTACT: "",
      PDSTracedAndVerified: "No",
      PDSTraced: "true",
      CliniHighPriority: "",
      StartConsultationPerformed: "",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "",
      FOLLOW_UP_URGENT: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      OversightValidationType: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "5",
      CasValidationUser: "CN=Disp Cont2/O=staging",
      CasValidationTime: "03/09/2025 11:34:39",
      CasValidationReason: "zdsvdsfv",
      SMS_HAS: "",
      SMS_SENT: "",
      SMS_LATEST_AT: "",
      SMS_LATEST_USER: "",
      SMS_LATEST_MESSAGE: "",
      SMS_COUNT: "",
      PDSAdminTrace: "",
      CallFAction: "",
      Cpl_furtherActionGPText: "",
      PLS_REASON: "",
      PLS_USER: "",
      PLS_TIME: "",
      PLS_ACTION: "",
      PLS_ACTIONTEXT: ""
    },
    {
      unid: "09050BE1CD57B1EC80258C99001CCEBA",
      name: "250670357",
      CallCCMS: "",
      Info: "",
      IsLocked: "",
      CallNo: "250670357",
      CallID: "250670357",
      CallNHSNo: "**********",
      CallService: "CAS",
      CallServiceSub: "",
      CallServiceOriginal: "",
      CallServiceAlt: "",
      CallMF: "Female",
      Call_HCP: "",
      CallDobIso: "2010-01-01",
      CallPatientTitle: "",
      CallAddress1: "First West Of England Ltd",
      CallAddress2: "Bus & Coach Station Marlborough Street",
      CallAddress3: "Bristol",
      CallAddress4: "",
      CallTown: "",
      CallPostCode: "BS1 3NU",
      UTC_Assigned: "",
      GOODSAM_IMAGE_STATUS: "",
      CallClassification: "Advice",
      CC: "Advice",
      CSC: "Follow Up",
      WalkIn: "0",
      CallUrgentYN: "No",
      Call1stContact: "01/07/2025 14:28:34",
      Call1stContactPathways: "",
      PathwaysCaseId: "",
      CallCreatedBy: "CN=Nick Wall3/O=cleouat",
      CallCName: "111_Comfort Call BS_TEST_NH",
      CallCRel: "Patient",
      BreachKey: "AdviceNo",
      ApplyBreach: "0",
      CallReceivedISO: "2025-05-29T12:05:00+01:00",
      CallReceivedTimeISO: "",
      BreachWarnActualTime: "2025-05-29T15:45:00+01:00",
      BreachPreActualTime: "2025-05-23T14:52:31+01:00",
      BreachActualTime: "2025-05-29T16:05:00+01:00",
      BreachPriority: "2",
      BreachLevel1Mins: "240",
      Source: "",
      BreachPriorityGroup: "PriorityNotDx",
      BreachPriorityLabel: "Less Urgent 4 hrs",
      CallWithBaseAckTime: "",
      CallReceivedTime: "",
      CallAppointmentTime: "",
      CallArrivedTime: "",
      dtArrivedTime: "",
      CallAge: "15 yrs",
      CallDoctorNameCN: "Disp Cont2",
      PatientName: "BS_TEST_NH, 111_Comfort Call",
      CallTriaged: "",
      CallSymptoms: "test",
      CallPtcl: "",
      CallStatus: "New",
      CallStatusValue: "1",
      PatientContactCode: "",
      PatientContactCode_count: "",
      PatientContactCode_Initial: "",
      PatientContactCode_Current_ForView: "",
      CallInformationalOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "",
      Dispatch_Vehicle: "",
      CallCompletedYN: "No",
      CallCompleted: "",
      CallCallback: "",
      CallTelNo: "02088 931550",
      CallTelNo_R: "07912626865",
      CallTelNoAlt_1: "",
      CallTelNoAltType_1: "",
      CallSurname: "BS_TEST_NH",
      CallForename: "111_Comfort Call",
      CallDoctorName: "CN=Disp Cont2/O=staging",
      CallSecondOpenCall: "",
      CallAdastraSent: "",
      CallAdastraLock: "0",
      TomTomOrderID: "",
      Linked_Call_ID: "",
      CallPractice: "A TEST PRACTICE",
      CallPracticeOCS: "G00239",
      SDec_Service: "0",
      CAS_Booking_Dr: "",
      CAS_Booking_Time: "",
      CallWarmTransferred: "",
      CHFinalDispositionCode: "P4h",
      CHFinalDispositionDescription:
        "The disposition is: Speak to a Clinician from our service Immediately - Emergency Treatment Centre V",
      FinalDispositionCode: "P4h",
      FinalDispositionDescription:
        "The disposition is: Speak to a Clinician from our service Immediately - Emergency Treatment Centre V",
      FLAG_REMOVE_FIRST_CONTACT: "0",
      PDSTracedAndVerified: "No",
      PDSTraced: "No",
      CliniHighPriority: "",
      StartConsultationPerformed: "1",
      ClinicalHub_111ToHubReason: "",
      Courtesy_User: "",
      Courtesy_Time: "",
      Courtesy_Count: "",
      Courtesy_Contact: "",
      CAS_TRANSFER_ERROR: "",
      Pathways_ITK_Send: "",
      ITK_111_Online: "0",
      AFT_appt_id: "",
      AFT_datetime_start: "",
      AFT_UNABLE_REASON: "",
      DAB_Id: "",
      DAB_StartDateTime: "",
      DAB_EndDateTime: "",
      AFT_time_start: "",
      AFT_datetime_start_ORIG: "",
      AFT_CANCELLED_REASON: "",
      ED_arrived: "",
      txtAppointmentBase: "",
      CareConnectAppointmentStart: "",
      IUC_CAS_AT_ONE_TIME: "1",
      IUC_Contract: "BrisDoc",
      SetBaseInfo_Base: "",
      Comfort_SENT_SERVICE: "",
      Comfort_SENT_SERVICE_TIME: "",
      Comfort_SENT_SERVICE2: "",
      Comfort_SENT_SERVICE2_TIME: "",
      Comfort_SMS2: "",
      Comfort_SMS_TIME2: "",
      Comfort_SMS: "",
      Comfort_SMS_TIME: "",
      Comfort_Cancelled_TIME: "",
      Comfort_Cancelled_REASON: "",
      Cov19Priority: "",
      dateAppointmentStart: "",
      EConsult_UTC: "",
      EConsult_code: "",
      EConsult_Priority: "",
      EConsult_PriorityLabel: "",
      EConsult_ITK_IN: "",
      EConsult_Status: "",
      EConsult_Source: "",
      EConsult_LivingArrangement: "",
      EConsult_AttendanceSource: "",
      EConsult_LiveOverseas: "",
      EConsult_ArrivalMethod: "",
      EConsult_SameProblem: "",
      EConsult_XRay: "",
      patient_alerts_count: "",
      Call_CaseComments: "",
      EMIS_CDB: "",
      EMIS_PatientId: "",
      EMIS_UserId: "",
      KMS_SC_MESSAGE: "",
      CareHomeName: "",
      ALL_VIEW_INCLUDE: "",
      cleoClientService: "FOLLOW_UP",
      COMPLETE_PREVENT: "",
      FOLLOW_UP_Active: "29/05/2025 12:05:00",
      FOLLOW_UP_URGENT: "1",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      OversightValidationType: "",
      Cpl_supportTypeRequired: "",
      CasValidationCount: "",
      CasValidationUser: "",
      CasValidationTime: "",
      CasValidationReason: "",
      SMS_HAS: "1",
      SMS_SENT: "0",
      SMS_LATEST_AT: "23/05/2025 14:24:36",
      SMS_LATEST_USER: "ITK",
      SMS_LATEST_MESSAGE:
        "Your details have been passed to us by NHS111.  We are working hard to contact you as soon as possible, but please be aware this can be longer than the timeframe given by NHS111. If this is the case, one of our call handlers will phone you to check how you are.  Severnside Integrated Urgent Care. ",
      SMS_COUNT: "1",
      PDSAdminTrace: "",
      CallFAction: "",
      Cpl_furtherActionGPText: "",
      PLS_REASON: "",
      PLS_USER: "",
      PLS_TIME: "",
      PLS_ACTION: "",
      PLS_ACTIONTEXT: ""
    }
  ],
  Page: {
    Enabled: 1,
    PageNumber: 1,
    PageSize: 1000,
    getRowCount: 38,
    getStartRowNumber: 1,
    TotalSearchRowCount: 38,
    getTotalNumberOfPages: 1
  }
} as any) as GridLegacyServerResponse;
