import { IAdapterPagedResponse } from "@/common/common-models";
import {
  ITomTomVehicle,
  IVehicle,
  IVehicleLegacy
} from "@/vehicles/vehicles-models";
import {
  ILegacyCleoServerResponse,
  ILegacyDojoResponse
} from "@/common/cleo-legacy-models";
import https from "@/common/https";
import { CLEO_CONFIG } from "@/common/config/config-";
import { CleoCommonService } from "@/common/cleo-common-service/cleo-common-service";

const cleoCommonService = new CleoCommonService();

export class VehiclesData {
  public getCleoVehicles(): Promise<ILegacyDojoResponse<IVehicleLegacy[]>> {
    if (process.env.NODE_ENV === "development") {
      return new Promise(function(resolve) {
        const callBack = (resp: ILegacyDojoResponse<IVehicleLegacy[]>) => {
          resolve(resp);
        };
        window.CallControllerClient.getDispatchCarDataStore(callBack);
      });
    } else {
      return https.get(
        CLEO_CONFIG.CLEO.XCLEO_PATH +
          "/xpbeaninterface.xsp?processformat=json&action=GETDISPATCHCARSDATASTORE",
        {
          responseType: "json"
        }
      );
    }
  }

  public getTomTomVehicles(
    lat: number,
    lng: number
  ): Promise<ITomTomVehicle[] | ILegacyCleoServerResponse<null>> {
    //{"errorCode":9160,"errorMsg":"The geo position (lat/lon) is invalid"}
    if (process.env.NODE_ENV === "development") {
      return Promise.resolve([
        {
          objectno: "Delta 38",
          objectuid: "1-44036-530A6B773",
          objectstate: 3,
          latitude: 52439692,
          longitude: 1561053,
          lineardistance: 5264895
        },
        {
          objectno: "Delta 42",
          objectuid: "1-44036-323B4EF21",
          objectstate: 3,
          latitude: 52453621,
          longitude: 1561626,
          lineardistance: 5266434
        },
        {
          objectno: "Delta 68",
          objectuid: "1-44036-2990F9530",
          objectstate: 3,
          latitude: 52468100,
          longitude: 1743276,
          lineardistance: 5266520
        }
      ]);
    } else {
      return https.get(
        CLEO_CONFIG.CLEO.CALL_DB +
          "/(agcleoapijson)?openagent&processformat=json&action=GET_TOMTOM_CAR_POSITIONS&LAT=" +
          lat +
          "&LNG=" +
          lng,
        {
          responseType: "json"
        }
      );
    }
  }

  public dispatchToVehicle(
    callId: string | number,
    vehicleUnid: string,
    lat: string,
    lng: string
  ): Promise<ILegacyCleoServerResponse<unknown>> {
    // const payload = {
    //   SID_CALL_IDENTIFIER: callId,
    //   CAR_IDENTIFIER: cleoCommonService.formatUserDominoName(vehicleId),
    //   LAT: lat === "0" ? "" : lat.toString().slice(0, 9),
    //   LNG: lng === "0" ? "" : lng.toString().slice(0, 9)
    // };

    if (process.env.NODE_ENV === "development") {
      return new Promise(function(resolve) {
        setTimeout(function() {
          resolve({
            DATA: "",
            RESULT: "SUCCESS",
            MESSAGE: "LOCALSERVER: TomTom Sent"
          });
        }, 1000);
      });
    } else {
      const latSend: string = lat === "0" ? "" : lat.toString().slice(0, 9);
      const lngSend: string = lng === "0" ? "" : lng.toString().slice(0, 9);

      const urlAction =
        "action=DISPATCHCAR_ASSIGN" +
        "&SID_CALL_IDENTIFIER=" +
        callId +
        "&CAR_IDENTIFIER=" +
        vehicleUnid +
        "&LAT=" +
        latSend +
        "&LNG=" +
        lngSend;

      return https.get(
        CLEO_CONFIG.CLEO.CALL_DB +
          "/(agcleoapijson)?openagent&processformat=json&" +
          urlAction,
        {
          responseType: "json"
        }
      );
    }
  }

  public retrieveFromVehicle(
    callId: string | number
  ): Promise<ILegacyCleoServerResponse<unknown>> {
    if (process.env.NODE_ENV === "development") {
      return new Promise(function(resolve) {
        setTimeout(function() {
          resolve({
            DATA: "",
            RESULT: "SUCCESS",
            MESSAGE: "LOCALSERVER: TomTom Sent"
          });
        }, 1000);
      });
    } else {
      return https.get(
        CLEO_CONFIG.CLEO.CALL_DB +
          "/(agcleoapijson)?openagent&processformat=json&action=DISPATCHCAR_RETRIEVE&SID_CALL_IDENTIFIER=" +
          callId,
        {
          responseType: "json"
        }
      );
    }
  }

  public getVehicles(): Promise<IAdapterPagedResponse<IVehicle>> {
    return Promise.resolve({
      CurrentPage: 1,
      RecordsPerPage: 1000,
      TotalPages: 1,
      TotalRecords: 3,
      Records: [
        {
          id: 1,
          name: "Delta 1",
          isActive: true,
          isTomTomActive: true,
          Lat: 51.9471,
          Long: 1.01504,
          unid: "eqwd2ew"
        },
        {
          id: 2,
          name: "Delta 2",
          isActive: false,
          isTomTomActive: true,
          Lat: 52.60504636138489,
          Long: 1.5714061870909082,
          unid: "eqwwefwefd2ew"
        },
        {
          id: 3,
          name: "Delta 3",
          isActive: false,
          isTomTomActive: true,
          Lat: 52.521543749102115,
          Long: 1.5092920264953642,
          unid: "eqwdewfewfw2ew"
        }
      ]
    });
  }
}
