import { ILegacyDojoResponse } from "@/common/cleo-legacy-models";
import { IBaseLookupFullLegacy } from "@/bases/base-models";

export const mockData: Record<string, IBaseLookupFullLegacy> = {
  "Norwich PCC": {
    description: "Norwich PCC",
    GPSLat: "52.6504",
    GPSLong: "1.26606",
    CCG_Code: "06W",
    keyAddress: "Bowthorpe Road",
    AdapterId: "76",
    keyTown: "Norwich",
    keyPostCode: "NR2 3TU",
    permalink: "CSIR-9PBE5V",
    InOut: "OUT",
    InIc24Area: ""
  },
  "Sevenoaks PCS": {
    description: "Sevenoaks PCS",
    GPSLat: "51.287945195005236",
    GPSLong: "0.1953486097163741",
    CCG_Code: "99J",
    keyAddress: "Hospital Rd",
    AdapterId: "82",
    keyTown: "Sevenoaks",
    keyPostCode: "TN13 3PG",
    permalink: "CSIR-9PBE5V",
    InOut: "OUT",
    InIc24Area: ""
  },
  "Great Yarmouth": {
    description: "Great Yarmouth",
    GPSLat: "52.605298896763514",
    GPSLong: "1.7199095244149538",
    CCG_Code: "5PR",
    keyAddress: "Pasteur Rd",
    AdapterId: "51",
    keyTown: "Great Yarmouth",
    keyPostCode: "NR31 0DW",
    permalink: "CSIR-9PBE5V",
    InOut: "IN",

    InIc24Area: ""
  }
};
