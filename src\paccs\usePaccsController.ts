import { reactive } from "@vue/composition-api";
import { loggerInstance } from "@/common/Logger";
import { IConsultCallLegacy } from "@/consults/consult-models-legacy";
// import { useHttpResponseController } from "@/common/useHttpController";
// import { PaccsData } from "@/paccs/paccs-data";

export function usePaccsController() {
  // const paccsData = new PaccsData();

  const legacyConsultFieldIds = [
    "CHUB_DiffDiagnosis",
    "CHUB_RedFlags",
    "CHUB_Presenting",
    "CHUB_Management",
    "CHUB_AppropHist",
    "CHUB_SafetyNet"
  ];

  const state = reactive({
    currentClinicianNotes: ""
  });

  function init() {
    // setUpEventListenersConsultFields();
  }

  function getCurrentClinicianNotes(): string {
    const legacyData: IConsultCallLegacy = window.CallControllerClient.createConsultFromCurrentHubCallForPathways();
    return window.CallControllerClient.getConsultPathwaysOutput(legacyData);
  }

  function setLegacyConsultFieldsEnabledState(fieldEnabled: boolean): void {
    legacyConsultFieldIds.forEach(domFieldName => {
      const domObject = window.document.getElementById(domFieldName);
      if (domObject) {
        loggerInstance.log(
          domFieldName + " found field, set enabled: " + fieldEnabled
        );
        (domObject as HTMLInputElement).disabled = !fieldEnabled;
      }
    });
  }

  // function setUpEventListenersConsultFields(): void {
  //   console.log("2setUpEventListenersConsultFields() ========>>>>>>>>>>>");
  //
  //   [
  //     "CHUB_DiffDiagnosis",
  //     "CHUB_RedFlags",
  //     "CHUB_Presenting",
  //     "CHUB_Management",
  //     "CHUB_AppropHist",
  //     "CHUB_SafetyNet"
  //   ].forEach(domFieldName => {
  //     const domObject = window.document.getElementById(domFieldName);
  //     if (domObject) {
  //       loggerInstance.log(domFieldName + " found field, set up eventListener");
  //       domObject.addEventListener("change", event => {
  //         const target = event.target;
  //         if (target) {
  //           loggerInstance.log(
  //             domFieldName + " value has changed, recalc for PACCS."
  //           );
  //           state.currentClinicianNotes = getCurrentClinicianNotes();
  //         }
  //       });
  //     }
  //   });
  // }

  // function resendNotes() {
  //   useHttpResponseController().getData(
  //     paccsData.postNotes("", state.currentClinicianNotes)
  //   );
  // }

  return {
    init,
    state,
    getCurrentClinicianNotes,
    setLegacyConsultFieldsEnabledState
  };
}
