<template>
  <select class="input-field" v-model="selectedItem" v-on:change="onSelected">
    <option
      v-for="paccsCareAdviceSetItem in state.paccsCareAdviceSetItems"
      :key="paccsCareAdviceSetItem.careAdviceID"
      :value="paccsCareAdviceSetItem"
      v-text="paccsCareAdviceSetItem.cxKeyword"
    ></option>
  </select>
</template>

<script lang="ts">
import {
  defineComponent,
  onBeforeMount,
  reactive,
  ref,
  watch,
  SetupContext,
  ComputedRef,
  computed
} from "@vue/composition-api";
import { CommonService } from "@/common/common-service";
import { IConsult } from "@/consults/consult-models";
import { ConsultsService } from "@/consults/consults-service";

const commonService: CommonService = new CommonService();
const consultsService: ConsultsService = new ConsultsService();

export default defineComponent({
  // type inference enabled
  name: "select-simple",
  components: {},
  props: {
    items: {
      default: () => {
        return [];
      }
    },
    value: {
      default: () => {
        return {} as unknown;
      }
    }
  },
  setup(props: { value: unknown; items: unknown[] }, context: SetupContext) {
    const selectedItem = ref({});

    watch(
      () => props.value,
      (newValue: unknown, oldValue: unknown) => {
        // props.items.forEach( (item: unknown)=> {
        //
        // })
        // selectedItem.value
      }
    );

    function onSelected() {
      context.emit("input");
    }

    return {
      selectedItem,
      onSelected
    };
  }
});
</script>

<style scoped></style>
