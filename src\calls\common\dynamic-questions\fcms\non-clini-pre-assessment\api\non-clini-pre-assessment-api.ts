import { getKey<PERSON><PERSON>tandard } from "@/common/api/keywords-api";
import { Question } from "@/common/ui/dynamic-question/models/dynamic-question-models";
import { mockNonCliniAssessment } from "@/calls/common/dynamic-questions/fcms/non-clini-pre-assessment/mock/mockNonCliniPreAssessment";
import { mockNonCliniPreAssessmentServer } from "@/calls/common/dynamic-questions/fcms/non-clini-pre-assessment/mock/mockNonCliniPreAssessmentServer";

/**
 * Get Non-Clinical Pre-Assessment questions for FCMS
 *
 * This API fetches the dynamic questions used for FCMS non-clinical pre-assessment triage.
 * The questions follow a conditional flow to determine patient priority and emergency routing.
 *
 * Server endpoint example:
 * https://fcms-server.nhs.uk/api/
 * xpbeaninterface.xsp?processformat=json&action=GETKEYWORDSTANDARDJSON&sid=NON_CLINI_PRE_ASSESSMENT~FCMS
 *
 * Expected server response format:
 * {
 *   "NON_CLINI_PRE_ASSESSMENT_FCMS": {
 *     "description": "NON_CLINI_PRE_ASSESSMENT_FCMS",
 *     "KeywordService": "FCMS",
 *     "codeID1": "{....json data....}",
 *   }
 * }
 *
 * @param service - The service identifier (e.g., "FCMS")
 * @returns Promise<Question[]> - Array of dynamic questions with conditional logic
 */
export function getNonCliniPreAssessmentQuestions(
  service?: string
): Promise<Question[]> {
  const serverKeyword = "NON_CLINI_PRE_ASSESSMENT";
  const keywordLookup = serverKeyword + "_FCMS";

  const urlParams = {
    action: "GETKEYWORDSTANDARDJSON",
    sid:
      serverKeyword + (service && service.length > 0 ? "~" + service : "~FCMS")
  };

  // Development mode - return mock data
  if (process.env.NODE_ENV === "development") {
    return new Promise(resolve => {
      setTimeout(() => {
        // Use server-format mock data to simulate real API response
        const resp = mockNonCliniAssessment;
        const data = (resp as any)[keywordLookup];

        if (data && data.codeID1) {
          const questions = JSON.parse(data.codeID1);
          resolve(questions);
        } else {
          // Fallback to direct mock data
          resolve(mockNonCliniAssessment);
        }
      }, 1500); // Simulate network delay
    });
  }

  // Production mode - call actual server API
  return getKeywordsStandard(urlParams)
    .then(resp => {
      // If string then convert to JSON object
      resp = typeof resp === "string" ? JSON.parse(resp) : resp;

      const data = resp[keywordLookup];
      if (data && data.codeID1) {
        return JSON.parse(data.codeID1);
      }

      // Return empty array if no data found
      return [];
    })
    .catch(error => {
      console.error(
        "Error fetching non-clinical pre-assessment questions:",
        error
      );
      // Return fallback mock data on error
      return mockNonCliniAssessment;
    });
}

/**
 * Get questions by specific assessment type
 * Future enhancement to support different assessment types
 *
 * @param assessmentType - Type of assessment (e.g., "EMERGENCY", "ROUTINE", "MENTAL_HEALTH")
 * @param service - The service identifier
 * @returns Promise<Question[]>
 */
export function getNonCliniPreAssessmentQuestionsByType(
  assessmentType: string,
  service?: string
): Promise<Question[]> {
  // For now, return the standard questions
  // This can be extended to support different question sets based on assessment type
  return getNonCliniPreAssessmentQuestions(service);
}
