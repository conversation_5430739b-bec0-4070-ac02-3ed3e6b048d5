import { createLocalVue } from "@vue/test-utils";
import Vuex from "vuex";
import {
  gridFilterStateStore,
  GRID_FILTER_STATE_STORE_CONST
} from "./grid-filter-state-store";
import { useGridFilterState } from "./useGridFilterState";
import { useButtonFiltersController } from "./generic-filters/models/useButtonFiltersController";
import { factoryButtonFiltersControllerState } from "./generic-filters/models/generic-filters-service";
import { SocketGroup } from "../grid-models";

const localVue = createLocalVue();
localVue.use(Vuex);

describe("Grid Filter Persistence", () => {
  let store: any;

  beforeEach(() => {
    store = new Vuex.Store({
      modules: {
        [GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME]: gridFilterStateStore
      }
    });
  });

  describe("Grid Filter State Store", () => {
    it("should initialize with empty filter states", () => {
      const state =
        store.state[
          GRID_FILTER_STATE_STORE_CONST
            .GRID_FILTER_STATE_STORE__CONST_MODULE_NAME
        ];
      expect(state.filterStates).toEqual({});
    });

    it("should store filter state for a grid", () => {
      const gridId: SocketGroup = "CasCalls";
      const filterState = factoryButtonFiltersControllerState();
      filterState.gridFilterUserInput.ASSIGNED_TO = true;

      store.dispatch(
        `${GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME}/setFilterState`,
        { gridId, filterState }
      );

      const savedState = store.getters[
        `${GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME}/getFilterState`
      ](gridId);

      expect(savedState).toBeDefined();
      expect(savedState.gridFilterUserInput.ASSIGNED_TO).toBe(true);
    });

    it("should return null for non-existent grid filter state", () => {
      const gridId: SocketGroup = "PLS";
      const savedState = store.getters[
        `${GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME}/getFilterState`
      ](gridId);

      expect(savedState).toBeNull();
    });

    it("should clear filter state for a specific grid", () => {
      const gridId: SocketGroup = "CasCalls";
      const filterState = factoryButtonFiltersControllerState();

      // Set state
      store.dispatch(
        `${GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME}/setFilterState`,
        { gridId, filterState }
      );

      // Verify it exists
      let savedState = store.getters[
        `${GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME}/getFilterState`
      ](gridId);
      expect(savedState).toBeDefined();

      // Clear it
      store.dispatch(
        `${GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME}/clearFilterState`,
        gridId
      );

      // Verify it's gone
      savedState = store.getters[
        `${GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME}/getFilterState`
      ](gridId);
      expect(savedState).toBeNull();
    });

    it("should clear all filter states", () => {
      const gridId1: SocketGroup = "CasCalls";
      const gridId2: SocketGroup = "PLS";
      const filterState = factoryButtonFiltersControllerState();

      // Set states for multiple grids
      store.dispatch(
        `${GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME}/setFilterState`,
        { gridId: gridId1, filterState }
      );
      store.dispatch(
        `${GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME}/setFilterState`,
        { gridId: gridId2, filterState }
      );

      // Clear all
      store.dispatch(
        `${GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME}/clearAllFilterStates`
      );

      // Verify all are gone
      const state =
        store.state[
          GRID_FILTER_STATE_STORE_CONST
            .GRID_FILTER_STATE_STORE__CONST_MODULE_NAME
        ];
      expect(state.filterStates).toEqual({});
    });
  });

  describe("Multiple Grid States", () => {
    it("should maintain separate filter states for different grids", () => {
      const casGridId: SocketGroup = "CasCalls";
      const plsGridId: SocketGroup = "PLS";

      const casFilterState = factoryButtonFiltersControllerState();
      casFilterState.gridFilterUserInput.ASSIGNED_TO = true;
      casFilterState.gridFilterUserInput.BREACHED = true;

      const plsFilterState = factoryButtonFiltersControllerState();
      plsFilterState.gridFilterUserInput.ASSIGNED_TO = false;
      plsFilterState.gridFilterUserInput.PDS_TRACED_AND_VERIFIED = false;

      // Set different states for different grids
      store.dispatch(
        `${GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME}/setFilterState`,
        { gridId: casGridId, filterState: casFilterState }
      );
      store.dispatch(
        `${GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME}/setFilterState`,
        { gridId: plsGridId, filterState: plsFilterState }
      );

      // Verify each grid has its own state
      const savedCasState = store.getters[
        `${GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME}/getFilterState`
      ](casGridId);
      const savedPlsState = store.getters[
        `${GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__CONST_MODULE_NAME}/getFilterState`
      ](plsGridId);

      expect(savedCasState.gridFilterUserInput.ASSIGNED_TO).toBe(true);
      expect(savedCasState.gridFilterUserInput.BREACHED).toBe(true);
      expect(
        savedCasState.gridFilterUserInput.PDS_TRACED_AND_VERIFIED
      ).toBeNull();

      expect(savedPlsState.gridFilterUserInput.ASSIGNED_TO).toBe(false);
      expect(savedPlsState.gridFilterUserInput.BREACHED).toBeNull();
      expect(savedPlsState.gridFilterUserInput.PDS_TRACED_AND_VERIFIED).toBe(
        false
      );
    });
  });
});
