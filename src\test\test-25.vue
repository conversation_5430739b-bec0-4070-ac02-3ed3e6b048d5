<template>
  <div>
    <div
      :title="'Socket Status: ' + state.status"
      class="grid-route-toolbar--socket-local standard-icon"
      :class="
        state.status === 'ON'
          ? 'standard-icon--socket-connected'
          : 'standard-icon--socket-not-connected'
      "
    ></div>
    {{ book.title }}<input v-model="book.title" />
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onMounted,
  onUpdated,
  onUnmounted
} from "@vue/composition-api";
import { reactive, ref } from "@vue/composition-api";
import { SOCKET_STATUS } from "@/socket/socket-controller";
export default defineComponent({
  name: "socket-ui-status-new",
  props: {
    socketStatus: {
      type: String,
      default: "Not Connected",
      validator: function(value: SOCKET_STATUS) {
        // The value must match one of these strings
        return ["Connected", "Not Connected"].indexOf(value) !== -1;
      }
    }
  },
  setup() {
    const state = ref("");
    const book = reactive({
      title: "Vue 3 Guide"
    });

    onMounted(() => {
      console.log(">>>>>>>>>>>>>>>>>>>mounted!");
    });
    onUpdated(() => {
      console.log(">>>>>>>>>>>>>>>>>>>onUpdated!");
    });
    onUnmounted(() => {
      console.log(">>>>>>>>>>>>>>>>>>>unmounted!");
    });

    return {
      state,
      book
    };
  }
});
</script>
