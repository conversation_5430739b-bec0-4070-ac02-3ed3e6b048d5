import {
  IBase,
  IBaseN<PERSON>,
  IsoDateTimeWithOffset
} from "@/common/common-models";
import { IClientDevice } from "@/keywords/keywords-models";

export interface IConsultPrescriptionItem extends IBase {
  UniqueDrugId: string;
  Name: string;
  Quantity: string;
  ISeps: boolean;
  Time: IsoDateTimeWithOffset;
}

export interface IConsultPrescription extends IBase {
  PrescriptionType: string;
  EpsScriptId: string[];
  PrescriptionItems: IConsultPrescriptionItem[];
}

export interface IConsultPrescriptionCleo extends IConsultPrescription {
  PrescriptionType: "FP10" | "FP10PREC" | "PGD";
}

export interface IConsultPrescriptionEps extends IConsultPrescription {
  PrescriptionType: "eps";
}

export interface IConsultLegacyData extends IBase {
  GpAssignedType: string;
  CasPriority: string;
  CasPriorityClass: "";
}

export interface IConsultObservation {
  BloodPressureUpper: string;
  BloodPressureLower: string;
  Temperature: string;
  TemperatureScaleId: number;
  Pulse: string; //  "Pulse (000)"
  IsPregnant: boolean;
  Sats1: string; //  "Sats 1 (000%)"
  Sats2: string;
  OnOxygen: true | false | null; //  "On Oxygen"
  Resp: string; //  "Resps (00)"
  AcvpuTypeId: number; //  Alert | Confused | Responds to Voice, etc.
  PeakFlow: string; //  "Peak Flow (000)"
  Gcs1: string; //  "GCS (00/15)"
  Gcs2: string; //  "GCS (00/15)"
  Cbg: string; //  "CBG (000.mmol)"
}

export interface IConsultServerOutbound {
  Id: number;
  Callno: number;
  BaseId: number;
  ClientId: string; //  Mobile submitting consult multiple times.  E.g. e3339110-ebda-48d9-a7e5-82ddd1b8dad5
  GPAssignedId: string;
  ClientDevice: IClientDevice;
  EpsScriptGuid: string;
  UniqueUserId: string; //  E.g. CN=Clinician Hub/O=staging

  Details: string;
  Objective: string;
  Plan: string;
  Assessment: string;

  UrgentAtStart: boolean;
  UrgentAtEnd: boolean;
  IsUserUsingSmartCard: boolean;
  TeleAssessment: boolean; // wmiu_tele  "I confirm that I have carried out a telephone consultation on a case with a base/visit classification"
  PatientDobChecked: boolean;
  Presenting: string;
  PastHistory: string;

  RedFlags: string;
  Diagnosis: string;
  Management: string;
  Empowering: string;
  SafetyNet: string;

  RepresentativeTypeId: number;
  //  TODO
  Representative: string; //	text field to write e.g. Joe Bloggs

  ClassAtStartId: number;
  ClassAtEndId: number;

  // TimeStart: "" | IsoDateTimeOffset;
  // TimeEnd: "" | IsoDateTimeOffset;
  TimeStart: string;
  TimeEnd: string;
  RoleId: number;

  LegacyData: IConsultLegacyData;

  FailedContactCodeId: number;

  Observation: null | IConsultObservation;

  Prescriptions: IConsultPrescription[];
}

export interface IConsult {
  Id: number;
  Callno: number;
  Base: IBaseName;
  ClientId: string; //  Mobile submitting consult multiple times.  E.g. e3339110-ebda-48d9-a7e5-82ddd1b8dad5
  GPAssigned: IBaseName;
  ClientDevice: IClientDevice;
  EpsScriptGuid: string;
  UniqueUserId: string; //  E.g. CN=Clinician Hub/O=staging

  Details: string;
  Objective: string;
  Plan: string;
  Assessment: string;

  UrgentAtStart: boolean;
  UrgentAtEnd: boolean;
  IsUserUsingSmartCard: boolean;
  TeleAssessment: boolean; // wmiu_tele  "I confirm that I have carried out a telephone consultation on a case with a base/visit classification"
  PatientDobChecked: boolean;
  Presenting: string;
  PastHistory: string;

  RedFlags: string;
  Diagnosis: string;
  Management: string;
  Empowering: string;
  SafetyNet: string;

  PatientRepresentative: IBaseName;
  //  TODO
  Representative: string; //	text field to write e.g. Joe Bloggs

  ClassAtStart: IBaseName;
  ClassAtEnd: IBaseName;

  // TimeStart: "" | IsoDateTimeOffset;
  // TimeEnd: "" | IsoDateTimeOffset;
  TimeStart: string;
  TimeEnd: string;
  Role: IBaseName;

  LegacyData: IConsultLegacyData;

  FailedContactCode: IBaseName;

  Observation: null | IConsultObservation;

  Prescriptions: IConsultPrescription[];
}
