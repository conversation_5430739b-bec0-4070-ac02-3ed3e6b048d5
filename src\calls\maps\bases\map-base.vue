<template>
  <div>
    <div class="map-base--title">
      <a href="#" v-on:click.prevent="handleClick" class="map-base--title-link">
        <span v-text="base.Name"></span>
      </a>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, SetupContext } from "@vue/composition-api";
import { CommonService } from "@/common/common-service";
import { IBaseSummary } from "@/bases/base-models";
const commonService: CommonService = new CommonService();

export default defineComponent({
  name: "map-base",
  components: {},
  props: {
    base: {
      required: true,
      type: Object as PropType<IBaseSummary>
    }
  },
  setup(props: { base: IBaseSummary }, context: SetupContext) {
    function handleClick() {
      context.emit("baseClicked", props.base);
    }

    return {
      handleClick
    };
  }
});
</script>

<style scoped>
.map-base--title {
  font-weight: 700;
  padding: 0 0 5px 0;
}
.map-base--title-link {
  text-decoration: none;
}
</style>
