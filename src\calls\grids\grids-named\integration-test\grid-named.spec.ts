import {
  ICleoCallSummary,
  ICleoService,
  IObjectBase
} from "@/calls/summary/call-summarry-models";
import {
  gridNamedIntegrationService,
  ICompareCallResult,
  IIntegrationCallSummary
} from "@/calls/grids/grids-named/integration-test/grid-named-integration-service";
import {
  ILegacyCleoCallSummary,
  ILegacyServerResponse
} from "@/common/cleo-legacy-models";
import { IAdapterPagedResponse } from "@/common/common-models";
import { CommonService } from "@/common/common-service";

const legacyJson = require("./cleo-legacy-data.json");
const adapterJson = require("./cleo-adapter-data.json");
const commonService = new CommonService();

const legacyData = (legacyJson as any) as ILegacyServerResponse<
  ILegacyCleoCallSummary
>;
const adapterData = (adapterJson.result as any) as IAdapterPagedResponse<
  ICleoCallSummary
>;

const integrationService = gridNamedIntegrationService();

describe("grid-named", () => {
  it("2100332244", () => {
    const oldCall = legacyData.items.find((callSummary: any) => {
      return (callSummary.CallNo = "2100332244");
    });

    expect(legacyData.items.length).toBe(11);
    expect(oldCall && oldCall.CallNo).toBe("2100332244");

    expect(adapterData.Records.length).toBe(4);
    const newCall = adapterData.Records.find(
      (callSummary: ICleoCallSummary) => {
        return (callSummary.CallNo = 2100332244);
      }
    );
    expect(newCall && newCall.CallNo).toBe(2100332244);
  });

  it("areDataSetsSameSize", () => {
    expect(
      integrationService.areDataSetsSameSize(legacyData, adapterData)
    ).toBe(false);
  });

  it("callDifferences", () => {
    const oldCall: ILegacyCleoCallSummary = legacyData.items.find(
      (callSummary: any) => {
        return (callSummary.CallNo = "2100332244");
      }
    ) as ILegacyCleoCallSummary;
    const newCall: ICleoCallSummary = adapterData.Records.find(
      (callSummary: ICleoCallSummary) => {
        return (callSummary.CallNo = 2100332244);
      }
    ) as ICleoCallSummary;

    const oldInt: IIntegrationCallSummary = integrationService.mapLegacyCallModel(
      oldCall!
    );
    const newInt: IIntegrationCallSummary = integrationService.mapNewCallModel(
      newCall!
    );

    let diffs: unknown | null = null;
    if (oldCall && newCall) {
      diffs = commonService.differenceBetweenTwoObjects(oldInt, newInt);
    }
    expect(diffs).toBe(false);

    diffs = integrationService.callDifferences(oldCall, newCall);
    expect(diffs).toBe(false);
  });

  it("compareQueues", () => {
    const legacyCalls = legacyData.items;
    expect(legacyCalls.length).toBe(11);

    const adapterCalls = adapterData.Records;
    expect(adapterCalls.length).toBe(4);

    const result = integrationService.compareQueues(legacyCalls, adapterCalls);
    expect(Object.keys(result).length).toBe(7);

    const callsWithDiffs = integrationService.callsWithDiffs(result);
    expect(callsWithDiffs.length).toBe(6);

    const callToTest: ICompareCallResult | undefined = callsWithDiffs.find(
      callWithDiff => {
        return callWithDiff.callNumber === 2100332230;
      }
    );
    expect(callToTest!.callNumber).toBe(2100332230);
    expect(callToTest!.diffs["CallUrgentYn"]).toBe(false);
  });
});
