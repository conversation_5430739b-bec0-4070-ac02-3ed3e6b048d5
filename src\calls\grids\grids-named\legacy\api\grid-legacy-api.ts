import { IGridDefinition } from "@/calls/grids/grid-models";
import { mapGridLegacyCallSummaryToICleoCallSummary } from "@/calls/grids/grids-named/legacy/models/grid-legacy-service";
import { IAdapterPagedResponse } from "@/common/common-models";
import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import { useConfigHelper } from "@/common/config/config-store";
import { GridLegacyServerResponse } from "@/calls/grids/grids-named/legacy/models/grid-legacy-models";
// import { queueCasMock } from "@/calls/grids/grids-named/legacy/test/queue-cas-mock";
import { queueUrgentFollowUpMock } from "@/calls/grids/grids-named/legacy/test/queue-urgent-follow-up";
// import { queueCasMockSource20250507 } from "@/calls/grids/grids-named/legacy/test/queue-cas-mock-2025-05-07";
// import { queueCasMockSource20250508b } from "@/calls/grids/grids-named/legacy/test/queue-cas-mock-2025-05-08b";
// import { queueOversightMockSource20250512a } from "@/calls/grids/grids-named/legacy/test/queue-oversight-mock-2025-5-12a";
// import { queueOversightErrorMock } from "@/calls/grids/grids-named/legacy/test/queue-oversight-error";
// import { queueCasMockYellow } from "@/calls/grids/grids-named/legacy/test/queue-cas-mock-yellow";
import { queueCasMockSource20250507 } from "@/calls/grids/grids-named/legacy/test/queue-cas-mock-2025-05-07";
import { queueCasMockSubClass } from "@/calls/grids/grids-named/legacy/test/queue-cas-mock-sub-class";
import { queueCasMockWeirdError } from "@/calls/grids/grids-named/legacy/test/queue-cas-mock-weird-error";
import { queueFollowUpMock } from "@/calls/grids/grids-named/legacy/test/queue-follow-up";
import { queueCasMockSource20250508a } from "@/calls/grids/grids-named/legacy/test/queue-cas-mock-2025-05-08a";

const configHelper = useConfigHelper();

/**
 * This should return the ENTIRE queue for a given "grid", obviously we need some
 * reasonable way to.

 * @param gridDefinition
 */
export function getLegacyGridData(
  gridDefinition: IGridDefinition
): Promise<IAdapterPagedResponse<ICleoCallSummary>> {
  if (!gridDefinition.legacyOptions) {
    throw Error(
      "getLegacyGridData() gridDefinition.legacyOptions is undefined"
    );
  }
  const url = gridDefinition.legacyOptions.url;
  console.log(
    "getLegacyGridData() url:" +
      url +
      " at: " +
      new Date() +
      ", gridDefinition:",
    gridDefinition
  );

  if (configHelper.isLocalDevServer.value) {
    let mockData = queueCasMockSource20250507; // Use the new mock data
    if (url.indexOf("OVERSIGHT_FOLLOW_UP_URGENT") > -1) {
      mockData = queueUrgentFollowUpMock;
    }

    return Promise.resolve(mockData).then(resp => {
      const items = resp.items.map(item => {
        return mapGridLegacyCallSummaryToICleoCallSummary(item);
      });

      const adapterPagedResponse: IAdapterPagedResponse<ICleoCallSummary> = {
        CurrentPage: 1,
        RecordsPerPage: 1000,
        TotalPages: 1,
        TotalRecords: resp.Count,
        Records: items
      };

      return adapterPagedResponse;
    });
  }

  //https://dr-sta-dom04.sehnp.nhs.uk/stage/calld.nsf/getViewData?openagent&vn=NAV_111_SORT_PRIORITY_BREACH&PAGE_ON=1&PAGE_NUM=1&COV19_ALL=ALL&dojo.preventCache=1739809306223
  // const url =
  //   CLEO_CONFIG.CLEO.CALL_DB + "/getViewData?Openagent&vn=CAS_FILTER_NEW";
  return window.localCache.getUrlDataWithCache(url, false).then(resp => {
    // map then filter out any with CallNo === 0
    const items = (resp as GridLegacyServerResponse).items
      .map(item => {
        return mapGridLegacyCallSummaryToICleoCallSummary(item);
      })
      .filter(item => item.CallNo !== 0);

    const callNoMap = new Map<number, ICleoCallSummary>();

    // Preserve original order, keeping only the first occurrence of each CallNo
    items.forEach(item => {
      if (!callNoMap.has(item.CallNo)) {
        callNoMap.set(item.CallNo, item);
      }
    });

    // Convert to array of values while preserving order
    const uniqueItems = Array.from(callNoMap.values());

    const adapterPagedResponse: IAdapterPagedResponse<ICleoCallSummary> = {
      CurrentPage: 1,
      RecordsPerPage: 1000,
      TotalPages: 1,
      TotalRecords: (resp as GridLegacyServerResponse).Count,
      Records: Object.values(uniqueItems)
    };

    return adapterPagedResponse;
  });
}
