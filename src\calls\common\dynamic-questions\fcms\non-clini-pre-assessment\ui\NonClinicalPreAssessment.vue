<template>
  <div class="non-clinical-pre-assessment">
    <div class="ic24-section-header">
      <h2 class="ic24-section-header__title">
        FCMS Non-Clinical Pre-Assessment
      </h2>
      <p class="ic24-section-header__description">
        Please answer the following questions to determine the appropriate
        priority and response for this case.
      </p>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="ic24-flex-center ic24-padding-large">
      <div class="ic24-loading-spinner-large"></div>
      <p class="ic24-margin-left">Loading assessment questions...</p>
    </div>

    <!-- Error State -->
    <div
      v-else-if="error"
      class="ic24-alert ic24-alert--error ic24-margin-bottom"
    >
      <p>{{ error }}</p>
      <button
        type="button"
        class="ic24-button ic24-button--secondary ic24-margin-top-small"
        @click="loadQuestions"
      >
        Retry
      </button>
    </div>

    <!-- Questions Form -->
    <div v-else-if="questions.length > 0">
      <DynamicQuestionForm
        :questions="questions"
        @input="onQuestionsAnswered"
        ref="questionForm"
      />

      <!-- Navigation Controls -->
      <div
        class="question-navigation ic24-margin-top ic24-flex-row ic24-flex-gap"
      >
        <button
          type="button"
          class="ic24-button ic24-button--secondary"
          :disabled="!canGoBack"
          @click="goBack"
        >
          ← Back
        </button>

        <span class="question-progress ic24-flex-1 ic24-text-center">
          Question {{ currentQuestionIndex + 1 }} of {{ totalQuestions }}
        </span>
      </div>

      <!-- Validation Summary -->
      <div
        v-if="validationErrors.length > 0"
        class="ic24-alert ic24-alert--warning ic24-margin-top"
      >
        <h4>Please answer the following mandatory questions:</h4>
        <ul class="ic24-margin-top-small">
          <li v-for="error in validationErrors" :key="error.id">
            {{ error.label }}
          </li>
        </ul>
      </div>

      <!-- Assessment Complete State -->
      <div
        v-if="isAssessmentComplete"
        class="ic24-alert ic24-alert--success ic24-margin-top"
      >
        <h4>Assessment Complete</h4>
        <p>
          All mandatory questions have been answered. The assessment is ready
          for submission.
        </p>

        <!-- Assessment Summary -->
        <div class="assessment-summary ic24-margin-top">
          <h5>Assessment Summary:</h5>
          <div class="summary-grid">
            <div
              v-for="answer in answeredQuestions"
              :key="answer.id"
              class="summary-item"
            >
              <strong>{{ answer.label }}:</strong>
              <span>{{ formatAnswer(answer) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- No Questions State -->
    <div v-else class="ic24-alert ic24-alert--info">
      <p>No assessment questions available at this time.</p>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  onMounted,
  computed,
  SetupContext
} from "@vue/composition-api";
import { Question } from "@/common/ui/dynamic-question/models/dynamic-question-models";
import { DynamicQuestionsOutput } from "@/common/ui/dynamic-question/models/dynamic-question-models";
import DynamicQuestionForm from "@/common/ui/dynamic-question/ui/DynamicQuestionForm.vue";
import { getNonCliniPreAssessmentQuestions } from "@/calls/common/dynamic-questions/fcms/non-clini-pre-assessment/api/non-clini-pre-assessment-api";

export default defineComponent({
  name: "NonClinicalPreAssessment",
  components: {
    DynamicQuestionForm
  },
  props: {
    service: {
      type: String,
      default: "FCMS"
    }
  },
  emits: ["assessment-complete", "assessment-updated"],
  setup(props: { service: string }, context: SetupContext) {
    // Reactive state
    const questions = ref<Question[]>([]);
    const loading = ref(false);
    const error = ref<string | null>(null);
    const assessmentOutput = ref<DynamicQuestionsOutput | null>(null);
    const questionForm = ref<any>(null);

    // Load questions from API
    const loadQuestions = async () => {
      loading.value = true;
      error.value = null;

      try {
        questions.value = await getNonCliniPreAssessmentQuestions(
          props.service
        );
      } catch (err) {
        console.error("Failed to load assessment questions:", err);
        error.value = "Failed to load assessment questions. Please try again.";
      } finally {
        loading.value = false;
      }
    };

    // Handle questions answered event from DynamicQuestionForm
    const onQuestionsAnswered = (output: DynamicQuestionsOutput) => {
      assessmentOutput.value = output;

      // Emit assessment updated event
      context.emit("assessment-updated", output);

      // Emit assessment complete event when all mandatory questions are answered
      if (output.isValid) {
        context.emit("assessment-complete", output);
      }
    };

    // Computed properties
    const validationErrors = computed(() => {
      return assessmentOutput.value?.questionsThatNeedAnswer || [];
    });

    const isAssessmentComplete = computed(() => {
      return assessmentOutput.value?.isValid || false;
    });

    const answeredQuestions = computed(() => {
      if (!assessmentOutput.value) return [];

      return questions.value.filter(question => {
        const answer = assessmentOutput.value!.answers[question.id];
        return answer !== null && answer !== undefined && answer !== "";
      });
    });

    // Navigation computed properties
    const currentQuestionIndex = computed(() => {
      if (!assessmentOutput.value?.currentQuestionId) return 0;
      const flow = assessmentOutput.value.questionFlow;
      return flow.indexOf(assessmentOutput.value.currentQuestionId);
    });

    const totalQuestions = computed(() => {
      return (
        assessmentOutput.value?.questionFlow.length || questions.value.length
      );
    });

    const canGoBack = computed(() => {
      return currentQuestionIndex.value > 0;
    });

    // Navigation methods
    const goBack = () => {
      if (questionForm.value) {
        questionForm.value.goToPreviousQuestion();
      }
    };

    // Format answer for display
    const formatAnswer = (question: Question): string => {
      if (!assessmentOutput.value) return "";

      const answer = assessmentOutput.value.answers[question.id];

      if (answer === null || answer === undefined) return "Not answered";

      // Handle different question types
      if (question.type === "radio" || question.type === "select") {
        if (question.options && Array.isArray(question.options)) {
          // If options are objects with label/value
          if (typeof question.options[0] === "object") {
            const option = (question.options as any[]).find(
              opt => opt.value === answer
            );
            return option ? option.label : String(answer);
          }
          // If options are simple strings
          return String(answer);
        }
        return String(answer);
      }

      if (question.type === "checkbox") {
        if (Array.isArray(answer)) {
          return answer.join(", ");
        }
        return String(answer);
      }

      return String(answer);
    };

    // Initialize component
    onMounted(() => {
      loadQuestions();
    });

    return {
      questions,
      loading,
      error,
      assessmentOutput,
      validationErrors,
      isAssessmentComplete,
      answeredQuestions,
      currentQuestionIndex,
      totalQuestions,
      canGoBack,
      loadQuestions,
      onQuestionsAnswered,
      goBack,
      formatAnswer
    };
  }
});
</script>

<style scoped>
.non-clinical-pre-assessment {
  max-width: 800px;
  margin: 0 auto;
}

.assessment-summary {
  background: var(--ic24-color-grey-50);
  border-radius: var(--ic24-border-radius);
  padding: var(--ic24-padding);
}

.summary-grid {
  display: grid;
  gap: var(--ic24-flex-gap);
  margin-top: var(--ic24-margin);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--ic24-padding-small);
  background: white;
  border-radius: var(--ic24-border-radius-small);
  border: 1px solid var(--ic24-color-grey-200);
}

.summary-item strong {
  color: var(--ic24-color-grey-700);
  min-width: 200px;
}

@media (max-width: 768px) {
  .summary-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--ic24-flex-gap-small);
  }

  .summary-item strong {
    min-width: auto;
  }
}
</style>
