import {
  differenceInSeconds,
  formatDistanceStrict,
  isAfter,
  parseISO
} from "date-fns";

/**
 * TODO
 * Convert this to CommonUtils so it's tree shakeable.  Converting this
 * would be too much work in 1 go ...I think.
 */
export class CommonService {
  public convertObjectToArray<T>(someObject: Record<string, T>): T[] {
    const arr: T[] = [];
    for (const key in someObject) {
      arr.push(someObject[key]);
    }
    return arr;
  }

  public convertArrayToObject<ObjectType, PropName extends keyof ObjectType>(
    prop: ((t: ObjectType) => string) | PropName,
    someArray: ObjectType[]
  ): Record<string, ObjectType> {
    if (!someArray || someArray.length === 0) {
      return {};
    }
    return someArray.reduce((accum, obj: ObjectType) => {
      const propValue: string = (((typeof prop === "function"
        ? prop(obj)
        : obj[prop]) as any) as string).toString();

      accum[propValue] = obj;
      return accum;
    }, {} as Record<string, ObjectType>);
  }

  /**
   * Use this if there is a one to many mapping.   E.g.
   * {
   *      "MA": [
   *          {city: "Boston"},
   *          {city: "Winchester"}
   *      ],
   *      "CT": [
   *          {city: "New Canaan"}
   *      ]
   * }
   * @param prop
   * @param someArray
   */
  public convertArrayToObjectArray<
    ObjectType,
    PropName extends keyof ObjectType
  >(
    prop: ((t: ObjectType) => string) | PropName,
    someArray: ObjectType[]
  ): Record<string, ObjectType[]> {
    if (!someArray || someArray.length === 0) {
      return {};
    }
    return someArray.reduce((accum, obj: ObjectType) => {
      const propValue: string = (((typeof prop === "function"
        ? prop(obj)
        : obj[prop]) as any) as string).toString();

      if (!accum[propValue]) {
        accum[propValue] = [];
      }
      accum[propValue].push(obj);

      return accum;
    }, {} as Record<string, ObjectType[]>);
  }

  /**
   *
   * @param hasThisIsoDateTime
   * @param seedDateTime            Is a Date as can initialise once and reuse.
   */
  public reachedThisTime(
    hasThisIsoDateTime: string | Date,
    reachedThisDateTime: string | Date = new Date()
  ): boolean {
    if (typeof hasThisIsoDateTime === "string") {
      hasThisIsoDateTime = parseISO(hasThisIsoDateTime);
    }

    if (typeof reachedThisDateTime === "string") {
      reachedThisDateTime = parseISO(reachedThisDateTime);
    }

    const secs = differenceInSeconds(hasThisIsoDateTime, reachedThisDateTime);
    return secs >= 0;
  }

  public prop<T, K extends keyof T>(obj: T, key: K): unknown {
    return obj[key];
  }

  public valueAsString(value: any): string {
    return value ? ((value as unknown) as string).toString() : "";
  }

  public fromNow(startDate: Date, endDate: Date = new Date()): string {
    if (!startDate) {
      return "NA";
    }
    const relString = formatDistanceStrict(endDate, startDate);
    return isAfter(startDate, endDate) ? `in ${relString}` : `${relString} ago`;
  }

  /**
   * N.B. Date objects, Function or Infinity will be lost
   * @param sourceObject
   */
  public simpleObjectClone<T>(sourceObject: T): T {
    if (typeof sourceObject === "undefined" || sourceObject === null) {
      return sourceObject;
    }
    return JSON.parse(JSON.stringify(sourceObject));
  }

  public unique<T>(objs: T[]): T[] {
    return [...new Set(objs)];
  }

  public findFirst<T>(pred: (t: T) => boolean, someArray: T[]): T | null {
    for (let i = 0; i < someArray.length; i++) {
      const someT = someArray[i];
      const res = pred(someT);
      if (res) {
        return someT;
      }
    }
    return null;
  }

  /**
   * Handles sorting on strings or numbers
   * @param prop
   * @param someArray
   * @param order
   */
  public sortArray<ObjectType, PropName extends keyof ObjectType>(
    prop: ((t: ObjectType) => string) | PropName,
    someArray: ObjectType[],
    order: "ASC" | "DESC" = "ASC"
  ): ObjectType[] {
    if (!someArray || someArray.length === 0) {
      return someArray;
    }

    return someArray.sort((a, b) => {
      const propValueA: unknown =
        typeof prop === "function" ? prop(a) : a[prop];
      const propValueB: unknown =
        typeof prop === "function" ? prop(b) : b[prop];

      if (typeof propValueA === "string" && typeof propValueB === "string") {
        const compA: string = (order === "ASC"
          ? propValueA
          : propValueB
        ).toUpperCase();
        const compB: string = (order === "ASC"
          ? propValueB
          : propValueA
        ).toUpperCase();
        // const compB: string = propValueB.toString().toUpperCase();

        if (compA < compB) {
          return -1;
        }
        if (compA > compB) {
          return 1;
        }
        // names must be equal
        return 0;
      }

      if (typeof propValueA === "number" && typeof propValueB === "number") {
        return order === "ASC"
          ? propValueA - propValueB
          : propValueB - propValueA;
      }

      return 0;
    });
  }

  public differenceBetweenTwoObjects<T>(obj1: T, obj2: T): any {
    //  TODO yeah I know, fix the linting stuff when get some time.
    let k;
    let kDiff;
    const diff = {};
    for (k in obj1) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      if (!obj1[k]) {
        //  do nothign
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
      } else if (typeof obj1[k] !== "object" || typeof obj2[k] !== "object") {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        if (!(k in obj2) || obj1[k] !== obj2[k]) {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          diff[k] = obj2[k];
        }

        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        // eslint-disable-next-line no-cond-assign
      } else if ((kDiff = this.differenceBetweenTwoObjects(obj1[k], obj2[k]))) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        diff[k] = kDiff;
      }
    }
    for (k in obj2) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      // eslint-disable-next-line no-prototype-builtins
      if (obj2.hasOwnProperty(k) && !(k in obj1)) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        diff[k] = obj2[k];
      }
    }
    for (k in diff) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // eslint-disable-next-line no-prototype-builtins
      if (diff.hasOwnProperty(k)) {
        return diff;
      }
    }
    return false;
  }
}

/**
 *
 * @param el
 */

export function getElementPosition(
  el: HTMLElement
): { top: number; left: number } {
  let x = 0;
  let y = 0;
  while (el && !isNaN(el.offsetLeft) && !isNaN(el.offsetTop)) {
    x += el.offsetLeft - el.scrollLeft;
    y += el.offsetTop - el.scrollTop;
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //  @ts-ignore
    el = el.offsetParent;
  }
  return { top: y, left: x };
}

export const ISO_FORMAT = "yyyy-MM-dd[T]HH:mm:ssZZ";

/**
 * Clears an array of undefined and nulls.
 * E.g. ["hi", 3, undefined, 33, {a: 1}, null, "xxx"] = > ["hi", 3, 33, {…}, "xxx"]
 * @param value
 */
export function notEmpty<TValue>(
  value: TValue | null | undefined
): value is TValue {
  return value !== null && value !== undefined;
}

/**
 * @param someArray
 */
export function makeArrayNotEmpty<SomeObject>(
  someArray: (SomeObject | null | undefined)[]
): SomeObject[] {
  return someArray.filter(notEmpty);
}
