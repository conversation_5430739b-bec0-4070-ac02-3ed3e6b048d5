# Grid Filter Auto-Apply Enhancement

## Problem Statement

The current implementation stores filter states per grid, but when a grid loads, it doesn't automatically apply the saved filters. Users need to manually open the filter dialog to see their saved filters applied. We need to automatically trigger filtering when a grid loads with saved filter state.

## Enhancement Solution

Add automatic filter application during grid initialization in `GridStandard2.vue` setup() function.

## Implementation Plan

### 1. Update GridStandard2.vue Setup Function

Add logic to check for saved filter state and automatically apply it after grid controller initialization:

```typescript
// In GridStandard2.vue setup() function, after gridController initialization
import { useGridFilterState } from "@/calls/grids/grid-filter/useGridFilterState";

const gridFilterState = useGridFilterState();

// After gridController is created
const gridController = useGridController(
  context,
  props.gridDefinition,
  gridControllerAppConfig
);

// Check for saved filter state and auto-apply
const savedFilterState = gridFilterState.getFilterState(props.gridDefinition.identifier);
if (savedFilterState) {
  console.log("GridStandard2: Found saved filter state for", props.gridDefinition.identifier);
  
  // Apply the saved filter state to the grid controller
  gridController.onGridFilterUserInputChanged(savedFilterState.gridFilterUserInput);
}
```

### 2. Alternative: Enhanced Grid Controller Approach

Modify `useGridController.ts` to accept and auto-apply saved filter state:

```typescript
// In useGridController.ts
export function useGridController(
  context: SetupContext,
  gridDefinition: IGridDefinition,
  gridControllerAppConfig: IGridControllerAppConfig
) {
  // ... existing code ...

  // Check for saved filter state during initialization
  const gridFilterState = useGridFilterState();
  const savedFilterState = gridFilterState.getFilterState(gridDefinition.identifier);
  
  if (savedFilterState) {
    // Initialize with saved filter state instead of default
    state.filterUserInput = simpleObjectClone(savedFilterState.gridFilterUserInput);
    setFilters(); // Apply the filters immediately
  }

  // ... rest of existing code ...
}
```

### 3. Enhanced ButtonFilters Integration

Ensure the ButtonFilters component properly initializes with the grid's current filter state:

```typescript
// In ButtonFilters.vue setup function
// When the filter dialog opens, it should show the current grid's filter state
buttonFiltersController.init({
  gridFilterUserInput: props.gridFilterUserInput, // This comes from gridController.state.filterUserInput
  gridId: props.gridId
});
```

## Implementation Flow

### Current Flow
```
Grid Loads → Default Empty Filters → User Opens Filter Dialog → Sees Saved Filters
```

### Enhanced Flow
```
Grid Loads → Check for Saved Filters → Auto-Apply Saved Filters → Grid Shows Filtered Data
```

## Code Changes Required

### 1. GridStandard2.vue Enhancement

```typescript
// Add import
import { useGridFilterState } from "@/calls/grids/grid-filter/useGridFilterState";

// In setup() function, after gridController creation
const gridFilterState = useGridFilterState();

// Auto-apply saved filters
const savedFilterState = gridFilterState.getFilterState(props.gridDefinition.identifier);
if (savedFilterState) {
  console.log("GridStandard2: Auto-applying saved filters for", props.gridDefinition.identifier);
  gridController.onGridFilterUserInputChanged(savedFilterState.gridFilterUserInput);
}
```

### 2. Visual Indicator Enhancement

Update the "Filters Applied" indicator to show when auto-applied filters are active:

```vue
<!-- In template -->
<span v-if="gridController.state.filters.length > 0">
  Filters Applied ({{ gridController.state.filters.length }})
</span>
```

### 3. Debug Logging

Add console logging to track filter auto-application:

```typescript
if (savedFilterState) {
  console.log("GridStandard2: Found saved filter state for", props.gridDefinition.identifier, savedFilterState);
  gridController.onGridFilterUserInputChanged(savedFilterState.gridFilterUserInput);
  console.log("GridStandard2: Applied", gridController.state.filters.length, "filters");
}
```

## Benefits of Enhancement

### User Experience
- **Immediate Filter Application**: Grids load with previously applied filters already active
- **Visual Feedback**: "Filters Applied" indicator shows immediately
- **Seamless Workflow**: No need to open filter dialog to see applied filters

### Technical Benefits
- **Consistent State**: Grid data matches filter UI state
- **Performance**: Filters applied during initial data load
- **Debugging**: Clear logging of filter auto-application

## Testing Strategy

### Manual Testing
1. Apply filters to CasCalls grid
2. Navigate to PLS grid
3. Return to CasCalls grid
4. Verify:
   - Grid data is automatically filtered
   - "Filters Applied" indicator shows
   - Filter dialog shows correct saved state

### Automated Testing
```typescript
// Test auto-application of saved filters
it('should auto-apply saved filters when grid loads', () => {
  // Set up saved filter state
  const gridId = 'CasCalls';
  const filterState = createMockFilterState();
  gridFilterStateStore.setFilterState(gridId, filterState);
  
  // Mount grid component
  const wrapper = mount(GridStandard2, {
    propsData: { gridDefinition: GRID_DEFINITIONS[gridId] }
  });
  
  // Verify filters are automatically applied
  expect(wrapper.vm.gridController.state.filters.length).toBeGreaterThan(0);
  expect(wrapper.find('[data-test="filters-applied"]').exists()).toBe(true);
});
```

## Implementation Priority

### High Priority
- Auto-apply saved filters during grid initialization
- Update visual indicators to reflect applied filters

### Medium Priority
- Enhanced logging for debugging
- Performance optimization for filter application

### Low Priority
- Advanced filter state validation
- Filter state migration support

This enhancement completes the filter persistence feature by ensuring saved filters are automatically applied when grids load, providing a seamless user experience.