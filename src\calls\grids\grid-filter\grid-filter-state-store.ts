import { Modu<PERSON> } from "vuex";
import { IRootState } from "@/store/store";
import { SocketGroup } from "@/calls/grids/grid-models";
import { ButtonFiltersControllerState } from "@/calls/grids/grid-filter/generic-filters/models/useButtonFiltersController";
import Vue from "vue";

export interface IGridFilterStateStoreState {
  filterStates: Record<SocketGroup, ButtonFiltersControllerState>;
}

export enum GRID_FILTER_STATE_STORE_CONST {
  GRID_FILTER_STATE_STORE__CONST_MODULE_NAME = "GRID_FILTER_STATE_STORE__CONST_MODULE_NAME",

  GRID_FILTER_STATE_STORE__MUTATION_SET_FILTER_STATE = "GRID_FILTER_STATE_STORE__MUTATION_SET_FILTER_STATE",
  GRID_FILTER_STATE_STORE__MUTATION_CLEAR_FILTER_STATE = "GRID_FILTER_STATE_STORE__MUTATION_CLEAR_FILTER_STATE",
  GRID_FILTER_STATE_STORE__MUTATION_CLEAR_ALL_FILTER_STATES = "GRID_FILTER_STATE_STORE__MUTATION_CLEAR_ALL_FILTER_STATES"
}

const state: IGridFilterStateStoreState = {
  filterStates: {} as Record<SocketGroup, ButtonFiltersControllerState>
};

const mutations = {
  [GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__MUTATION_SET_FILTER_STATE](
    state: IGridFilterStateStoreState,
    payload: { gridId: SocketGroup; filterState: ButtonFiltersControllerState }
  ): void {
    Vue.set(state.filterStates, payload.gridId, payload.filterState);
  },

  [GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__MUTATION_CLEAR_FILTER_STATE](
    state: IGridFilterStateStoreState,
    gridId: SocketGroup
  ): void {
    Vue.delete(state.filterStates, gridId);
  },

  [GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__MUTATION_CLEAR_ALL_FILTER_STATES](
    state: IGridFilterStateStoreState
  ): void {
    state.filterStates = {} as Record<
      SocketGroup,
      ButtonFiltersControllerState
    >;
  }
};

const getters = {
  getFilterState: (state: IGridFilterStateStoreState) => (
    gridId: SocketGroup
  ): ButtonFiltersControllerState | null => {
    return state.filterStates[gridId] || null;
  },

  hasFilterState: (state: IGridFilterStateStoreState) => (
    gridId: SocketGroup
  ): boolean => {
    return !!state.filterStates[gridId];
  },

  getAllFilterStates: (
    state: IGridFilterStateStoreState
  ): Record<SocketGroup, ButtonFiltersControllerState> => {
    return state.filterStates;
  }
};

const actions = {
  setFilterState(
    { commit }: any,
    payload: { gridId: SocketGroup; filterState: ButtonFiltersControllerState }
  ) {
    commit(
      GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__MUTATION_SET_FILTER_STATE,
      payload
    );
  },

  clearFilterState({ commit }: any, gridId: SocketGroup) {
    commit(
      GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__MUTATION_CLEAR_FILTER_STATE,
      gridId
    );
  },

  clearAllFilterStates({ commit }: any) {
    commit(
      GRID_FILTER_STATE_STORE_CONST.GRID_FILTER_STATE_STORE__MUTATION_CLEAR_ALL_FILTER_STATES
    );
  }
};

export const gridFilterStateStore: Module<
  IGridFilterStateStoreState,
  IRootState
> = {
  namespaced: true,
  state,
  mutations,
  getters,
  actions
};
