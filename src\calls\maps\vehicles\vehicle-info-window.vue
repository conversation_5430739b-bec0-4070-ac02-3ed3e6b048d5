<template>
  <div class="cleo-map--marker-wrapper">
    <div class="cleo-map--marker-header">
      <div class="cleo-map--marker-label">Call</div>
      <div class="cleo-map--marker-data">
        <span v-text="vehicle.name"></span>
      </div>
    </div>

    <div class="cleo-map--marker-body">
      <div class="cleo-map--marker-section">
        <div class="cleo-map--marker-label">Active</div>
        <div class="cleo-map--marker-data">
          <span v-text="vehicle.isActive ? 'Active' : 'Not Active'"></span>
        </div>
      </div>

      <div class="cleo-map--marker-section">
        <div class="cleo-map--marker-full">
          <CallsSmallTable
            :cleo-call-summaries="cleoCallSummaries"
            v-on:routeCall="routeCall"
          ></CallsSmallTable>
        </div>
      </div>

      <div class="cleo-map--marker-section">
        <button
          class="adapter-button adapter-width-auto adapter-button--green"
          v-on:click.stop="$emit('showAssignedCalls', vehicle)"
        >
          <span>Show Locations</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  SetupContext
} from "@vue/composition-api";
import { IVehicle } from "@/vehicles/vehicles-models";
import { ICleoCallSummary } from "@/calls/summary/call-summarry-models";
import CallsSmallTable from "@/calls/maps/calls/calls-small-table.vue";
import { getMapControllerInstance } from "@/calls/maps/map-controller-factory";
import { MapService } from "@/calls/maps/map-service";

export default defineComponent({
  name: "call-info-window",
  components: { CallsSmallTable },
  props: {
    vehicle: {
      required: true,
      type: Object as PropType<IVehicle>
    },
    cleoCallSummaries: {
      type: Array as PropType<ICleoCallSummary[]>
    }
  },
  setup(
    props: { vehicle: IVehicle; cleoCallSummaries: ICleoCallSummary[] },
    context: SetupContext
  ) {
    const mapService = new MapService();

    function routeCall(cleoCallSummary: ICleoCallSummary) {
      const mapController = getMapControllerInstance();
      const callMarker = mapService.getMarker(
        mapService.getMarkerId(cleoCallSummary, "CALL"),
        mapController.cleoMapControllerState.calls.markers
      );

      const vehicleMarker = mapService.getMarker(
        mapService.getMarkerId(props.vehicle, "CAR"),
        mapController.cleoMapControllerState.cars.markers
      );

      if (vehicleMarker && callMarker) {
        mapController.calculateRouteBetweenTwoMarkers(
          vehicleMarker,
          callMarker
        );
      }
    }

    return {
      routeCall
    };
  }
});
</script>

<style scoped>
.map-base--title {
  font-weight: 700;
  padding: 0 0 5px 0;
}
/*.map-base--title-link {*/
/*  text-decoration: none;*/
/*}*/
.cleo-map--marker-header {
  font-weight: 600;
  /*margin-bottom: 10px;*/
}

.cleo-map--marker-body {
  margin-top: 10px;
}

.cleo-map--marker-section {
  margin-top: 10px;
  padding-top: 5px;
  border-top: 1px solid #e8e8e8;
}

.cleo-map--marker-label {
  width: 100px;
  display: inline-block;
  vertical-align: top;
}

.cleo-map--marker-data {
  width: 200px;
  display: inline-block;
}

.cleo-map--marker-full {
  width: 100%;
  display: inline-block;
}

.cleo-map--marker-data-large {
  max-height: 100px;
  overflow: auto;
}

.cleo-map--marker-button-dispatch {
  float: right;
}
</style>
