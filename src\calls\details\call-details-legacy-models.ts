import { CLeoPermissionServerResponse } from "@/permissions/permission-models";
import { GENDER, GUID } from "@/common/common-models";
import { CompleteFinalAction } from "@/calls/details/complete/complete-models";

export interface ICallDetailLegacyFieldData<DataType> {
  VALUE: DataType | DataType[];
}

export interface ICallDetailLegacy {
  CallNo: ICallDetailLegacyFieldData<string>;
  CallSurname: ICallDetailLegacyFieldData<string>;
  CallForename: ICallDetailLegacyFieldData<string>;
  CallMF: ICallDetailLegacyFieldData<GENDER>;
  CallAddress1: ICallDetailLegacyFieldData<string>;
  CallAddress2: ICallDetailLegacyFieldData<string>;
  CallAddress3: ICallDetailLegacyFieldData<string>;
  CallAddress4: ICallDetailLegacyFieldData<string>;
  CallTown: ICallDetailLegacyFieldData<string>;
  CallPostCode: ICallDetailLegacyFieldData<string>;
  CallClassification: ICallDetailLegacyFieldData<string>;
  CallAge: ICallDetailLegacyFieldData<string>;
  CallDOB: ICallDetailLegacyFieldData<string>;
  CallAgeClass: ICallDetailLegacyFieldData<string>;
  CallSymptoms: ICallDetailLegacyFieldData<string>;
  CallUrgentYn: ICallDetailLegacyFieldData<"Yes" | "No">;
  Dispatch_Vehicle?: ICallDetailLegacyFieldData<string>;
  CareHomeId: ICallDetailLegacyFieldData<string>;
  CareHomeName: ICallDetailLegacyFieldData<string>;
  CallService: ICallDetailLegacyFieldData<string>;
  CallServiceId: ICallDetailLegacyFieldData<string>;
  CallServiceType: ICallDetailLegacyFieldData<string>;
  IUC_Contract: ICallDetailLegacyFieldData<string>;
  IUC_ContractID: ICallDetailLegacyFieldData<string>;
  cleoClientService: ICallDetailLegacyFieldData<string>; // E.g. MENTAL_HEALTH, OUT_OF_HOURS_PROFESSIONAL_LINE, PAEDIATRICS, etc.
  CallTelNo_R: ICallDetailLegacyFieldData<string>;
  flag_complete: ICallDetailLegacyFieldData<"1" | "0" | "">;
  PathwaysCaseId: ICallDetailLegacyFieldData<GUID>;
  PathwaysCaseId_ITK_IN: ICallDetailLegacyFieldData<GUID>;
  PathwaysCaseId_FROM_ITK: ICallDetailLegacyFieldData<GUID>;
  ContainsRehydratedCase: ICallDetailLegacyFieldData<"1" | "0" | "">;
  PACCS_EVER?: ICallDetailLegacyFieldData<"1" | "0" | "">;

  USER_CONFIG?: CLeoPermissionServerResponse;
  PatientContactCode_Events: ICallDetailLegacyFieldData<string[]>;

  CHFinalDispositionCode: ICallDetailLegacyFieldData<GUID>;
  CHFinalDispositionDescription: ICallDetailLegacyFieldData<GUID>;
  FinalDispositionCode: ICallDetailLegacyFieldData<GUID>;
  FinalDispositionDescription: ICallDetailLegacyFieldData<GUID>;

  //  <Save & Return / End Assessment>
  Cpl_Action: ICallDetailLegacyFieldData<CompleteFinalAction>;

  Cpl_managedHow: ICallDetailLegacyFieldData<string>;
  Cpl_contactMade: ICallDetailLegacyFieldData<string>;
  Cpl_outcome: ICallDetailLegacyFieldData<string>;
  Cpl_outcomeSub: ICallDetailLegacyFieldData<string>;
  Cpl_otherOutcome: ICallDetailLegacyFieldData<string>;
  Cpl_failedContactReason: ICallDetailLegacyFieldData<string>;
  Cpl_patientRiskAssessment: ICallDetailLegacyFieldData<string>;
  Cpl_patientRiskAssessmentAction: ICallDetailLegacyFieldData<string>;
  Cpl_insufficientContactAttempts: ICallDetailLegacyFieldData<string>;
  Cpl_exitReason: ICallDetailLegacyFieldData<string>;
  Cpl_failedContactWarning: ICallDetailLegacyFieldData<string>;

  CallInformationalOutcomes: ICallDetailLegacyFieldData<string>;
  CallInformationalSubOutcomes: ICallDetailLegacyFieldData<string>;
  CallInformationalOutcomesComment: ICallDetailLegacyFieldData<string>;

  Cpl_onwardReferral: ICallDetailLegacyFieldData<string>;
  Cpl_onwardReferralText: ICallDetailLegacyFieldData<string>;
  Cpl_furtherActionGP: ICallDetailLegacyFieldData<string>;
  Cpl_furtherActionGPText: ICallDetailLegacyFieldData<string>;
  Cpl_clinicalValidation: ICallDetailLegacyFieldData<string>;

  Cpl_taxi: ICallDetailLegacyFieldData<string>;
  Cpl_vulnerabilityAdult: ICallDetailLegacyFieldData<string>;
  Cpl_vulnerabilityChild: ICallDetailLegacyFieldData<string>;
  Cpl_mcaAssessed: ICallDetailLegacyFieldData<string>;
  Cpl_mcaRequired: ICallDetailLegacyFieldData<string>;

  CPL_READCODES: ICallDetailLegacyFieldData<string[]>;

  Cpl_medicationIssuedFromStock: ICallDetailLegacyFieldData<string>;
  Cpl_nonClinicalSupportToComplete: ICallDetailLegacyFieldData<string>;
  Cpl_mhClinicianSignOffRequired: ICallDetailLegacyFieldData<string>;
  Cpl_supportTypeRequired: ICallDetailLegacyFieldData<string>;
  Cpl_supportTypeRequiredComments: ICallDetailLegacyFieldData<string>;

  Cpl_AuditQuestions: ICallDetailLegacyFieldData<string>;

  Cpl_nonClinicalReason: ICallDetailLegacyFieldData<string>;
  Cpl_nonClinicalReasonComment: ICallDetailLegacyFieldData<string>;

  //  </Save & Return / End Assessment>
}

export type CallDetailLegacyProps = keyof ICallDetailLegacy;
export type CallDetailLegacyFieldName = keyof Omit<
  ICallDetailLegacy,
  "USER_CONFIG"
>;
// export const CallDetailLegacyFields = [
//   "CallNo",
//   "CallSurname",
//   "CallForename",
//   "CallAddress1",
//   "CallAddress2",
//   "CallAddress3",
//   "CallAddress4",
//   "CallTown",
//   "CallPostCode",
//   "CallClassification",
//   "CallAge",
//   "CallAgeClass",
//   "CallSymptoms",
//   "CallUrgentYn",
//   "DispatchVehicle"
// ] as const;
// export type CallDetailLegacyField = typeof CallDetailLegacyFields[number];
//
// // When a call gets sent up, it also adds these extar fields, e.g. perms, consults
// export const CallDetailLegacyFieldExtras = ["USER_CONFIG"] as const;
// export type CallDetailLegacyFieldExtra = typeof CallDetailLegacyFieldExtras[number];
//
// export type CallDetailLegacyFieldAll =
//   | CallDetailLegacyField
//   | CallDetailLegacyFieldExtra;
//
// //  Having the "string", whilst makes dev quicker, is actually prob going to make life harder.  List all fields in CallDetailLegacyFields
// export type CallDetailDataLegacy = Record<
//   string | CallDetailLegacyField,
//   {
//     VALUE: string[];
//   }
// >;
//
// export type CallDetailDataLegacyExtra = {
//   USER_CONFIG: CLeoPermissionServerResponse;
// };
//
// export type CallDetailDataLegacyAll =
//   | CallDetailDataLegacy
//   | CallDetailDataLegacyExtra;
