import { ICareHome } from "@/carehome/carehome-models";
import {
  ICallDetail,
  ICallDetailState,
  legacyDomId,
  targetCallDetailProp,
  UiDomMapFromLegacyToNew
} from "@/calls/details/call-details-models";
import { CallDetailLegacyFieldName } from "@/calls/details/call-details-legacy-models";
import { reactive, computed } from "@vue/composition-api";
import * as CallDetailService from "@/calls/details/call-detail-service";
import { ICompleteControllerState } from "@/calls/details/complete/complete-models";
import { simpleObjectClone } from "@/common/common-utils";
import { FollowUpInputState } from "@/calls/details/complete/components/followup/models/follow-up-models";
import { getFollowUpStateDisplayText } from "@/calls/details/complete/components/followup/models/follow-up-service";

export function useCallDetailController() {
  const state: ICallDetailState = reactive(
    CallDetailService.factoryICallDetailState()
  );

  const domSpanObservers: MutationObserver[] = [];
  const domInputs: HTMLInputElement[] = [];

  function careHomeSelected(careHome: ICareHome) {
    let careHomeIdInput: HTMLInputElement = document.getElementById(
      "CareHomeId"
    ) as HTMLInputElement;

    if (careHomeIdInput) {
      careHomeIdInput.value = careHome.id.toString();
      return;
    }

    const container = document.getElementById("cleo_content");
    if (container) {
      careHomeIdInput = document.createElement("input");
      careHomeIdInput.type = "text";
      careHomeIdInput.id = "CareHomeId";
      careHomeIdInput.name = "CareHomeId";
      careHomeIdInput.value = careHome.id.toString();
      container.appendChild(careHomeIdInput);
    }
  }

  function onTelephoneChanged(telephoneNumber: string): void {
    mapValueToLegacyUiExistingCase("CallTelNo_R", telephoneNumber);
  }

  /**
   * Legacy form uses <span> <div> <input> to store values!  This makes
   * hard to "map" legacy model (doesn't exist apart from initial load).
   * E.g. Return tel number changes, need that to be reflected in model.
   */
  function mapLegacyUiToModel() {
    observeSpan("CallTelNo_R");
    // observeSpan("CallForename");
    observeInput("CallSurname");
  }

  function observeSpan(domId: legacyDomId) {
    const target: HTMLElement | null = document.getElementById(domId);

    if (target) {
      const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
          // console.info("EVENT TRIGGER: " + mutation.target.id);
          updateCallDetailModelFromLegacyDom(domId, target?.innerText);
        });
      });

      const config = { attributes: true, childList: true, characterData: true };
      observer.observe(target, config);
      domSpanObservers.push(observer);
    }
  }

  function observeInput(domId: legacyDomId) {
    const target: HTMLInputElement | null = document.getElementById(
      domId
    ) as HTMLInputElement;
    if (target) {
      target.addEventListener("change", addChangeEventListener);
      domInputs.push(target);
    }
  }

  function addChangeEventListener(evt: Event) {
    updateCallDetailModelFromLegacyDom(
      (evt.target as HTMLInputElement).id as legacyDomId,
      (evt.target as HTMLInputElement).value
    );
  }

  function updateCallDetailModelFromLegacyDom(
    domId: legacyDomId,
    domValue: string
  ) {
    const targetProp: targetCallDetailProp = UiDomMapFromLegacyToNew[domId];
    state.callDetail[targetProp] = domValue;
    console.info(domId + ": setting prop: " + targetProp + ": to: " + domValue);
  }

  function destroySpanObservers() {
    domSpanObservers.forEach(obs => {
      console.log("destroySpanObservers");
      obs.disconnect();
    });
  }

  function destroyInputObservers() {
    domInputs.forEach(target => {
      console.log("destroyInputObservers");
      target.removeEventListener("change", addChangeEventListener);
    });
  }

  function destroy() {
    destroySpanObservers();
    destroyInputObservers();
  }

  function mapValueToLegacyUiExistingCase(
    fieldName: keyof ICallDetail,
    fieldValue: string
  ): void {
    const fieldMap: Partial<Record<
      keyof ICallDetail,
      CallDetailLegacyFieldName
    >> = {
      CallTelNo_R: "CallTelNo_R"
    };

    const targetFieldName: CallDetailLegacyFieldName | undefined =
      fieldMap[fieldName];
    if (targetFieldName) {
      window.CallControllerClient.setFieldValue(targetFieldName, fieldValue);
    }
  }

  function launchSaveAndReturn() {
    state.completeCase.process = "SAVE_AND_RETURN_PROCESS";
    launchComplete();
  }

  function launchEndAssessment() {
    state.completeCase.process = "COMPLETE_PROCESS";
    launchComplete();
  }

  function launchComplete() {
    state.completeCase.show = true;
  }

  function cancelComplete(completeControllerState: ICompleteControllerState) {
    //  No need to map anything to case.
    console.info("cancelComplete", completeControllerState);
    state.completeCase.show = false;
  }

  function setFollowUpInputState(followUpInputState: FollowUpInputState) {
    // const stateLocal = simpleObjectClone(state);
    // stateLocal.followUpInputState = simpleObjectClone(followUpInputState);
    // Object.assign(state, stateLocal);
    state.followUpInputState = simpleObjectClone(followUpInputState);
    state.showFollowUp = false;
  }

  const getFollowUpInputStateDisplayText = computed(() => {
    return getFollowUpStateDisplayText(state.followUpInputState);
  });

  function processComplete(completeControllerState: ICompleteControllerState) {
    console.info("processComplete", completeControllerState);
    state.completeCase.show = false;

    //  Map Complete state to Legacy UI.
    mapCompleteStateToLegacyUi(completeControllerState);

    //  Hook into Legacy "Complete" processes.
    if (completeControllerState.finalAction === "COMPLETE") {
      setCompleteLegacyFields(true);
    }

    //  saveNewCall();
    if (CallControllerClient.isNewCall) {
      CallControllerClient.saveNewCall();
    } else {
      CallControllerClient.submitDocumentPromise(state.callDetail.CallNo, true);
    }
  }

  function saveAndReturn(completeControllerState: ICompleteControllerState) {
    console.info("saveAndReturnToQueue", completeControllerState);
    mapCompleteStateToLegacyUi(completeControllerState);
    state.completeCase.show = false;

    //  Hook into Legacy "Save and Return" process..
    setCompleteLegacyFields(false);
    CallControllerClient.submitDocumentPromise(state.callDetail.CallNo, true);
  }

  function setCompleteLegacyFields(isCompleting: boolean) {
    CallControllerClient.setFieldValue(
      "flag_complete",
      isCompleting ? "1" : ""
    );
    CallControllerClient.ACTION_COMPLETE = isCompleting;
  }

  function mapCompleteStateToLegacyUi(
    completeControllerState: ICompleteControllerState
  ) {
    window.CallControllerClient.addFieldToModel(
      "Cpl_Action",
      completeControllerState.finalAction
    );

    window.CallControllerClient.addFieldToModel(
      "Cpl_managedHow",
      completeControllerState.userResponse.howManaged.value
    );
    window.CallControllerClient.addFieldToModel(
      "Cpl_onwardReferral",
      completeControllerState.userResponse.patientReferredTo.referredTo.value
    );
    window.CallControllerClient.addFieldToModel(
      "Cpl_onwardReferralText",
      completeControllerState.userResponse.patientReferredTo.referredText
    );
    window.CallControllerClient.addFieldToModel(
      "Cpl_furtherActionGP",
      completeControllerState.userResponse.patientReferredTo.furtherActionGP
        .value
    );

    window.CallControllerClient.addFieldToModel(
      "Cpl_furtherActionGPText",
      completeControllerState.userResponse.patientReferredTo.furtherActionGPText
    );

    //  <outcomes>
    window.CallControllerClient.addFieldToModel(
      "Cpl_outcome",
      completeControllerState.userResponse.outcomes.outcome
    );
    window.CallControllerClient.addFieldToModel(
      "Cpl_outcomeSub",
      completeControllerState.userResponse.outcomes.subOutcome
    );
    window.CallControllerClient.addFieldToModel(
      "Cpl_otherOutcome",
      completeControllerState.userResponse.outcomes.subOutcome
    );

    window.CallControllerClient.addFieldToModel(
      "CallInformationalOutcomes",
      completeControllerState.userResponse.outcomes.outcome
    );
    window.CallControllerClient.addFieldToModel(
      "CallInformationalSubOutcomes",
      completeControllerState.userResponse.outcomes.subOutcome
    );
    window.CallControllerClient.addFieldToModel(
      "CallInformationalOutcomesComment",
      completeControllerState.userResponse.outcomes.otherOutcome
    );
    //  </outcomes>

    window.CallControllerClient.addFieldToModel(
      "Cpl_failedContactReason",
      completeControllerState.userResponse.failedContactReason.value
    );
    window.CallControllerClient.addFieldToModel(
      "Cpl_patientRiskAssessment",
      completeControllerState.userResponse.patientRiskAssessment.risk.value
    );
    window.CallControllerClient.addFieldToModel(
      "Cpl_patientRiskAssessmentAction",
      completeControllerState.userResponse.patientRiskAssessment.actionTaken
    );
    window.CallControllerClient.addFieldToModel(
      "Cpl_insufficientContactAttempts",
      completeControllerState.userResponse.insufficientContactAttempts.value
    );
    window.CallControllerClient.addFieldToModel(
      "Cpl_exitReason",
      completeControllerState.userResponse.exitReason.value
    );
    window.CallControllerClient.addFieldToModel(
      "Cpl_failedContactWarning",
      completeControllerState.userResponse.failedContactWarning.value
    );

    window.CallControllerClient.addFieldToModel(
      "Cpl_clinicalValidation",
      completeControllerState.userResponse.clinicalValidation.value
    );

    window.CallControllerClient.addFieldToModel(
      "Cpl_taxi",
      completeControllerState.userResponse.taxi.value
    );

    //  <vulnerability>
    window.CallControllerClient.addFieldToModel(
      "Cpl_vulnerabilityAdult",
      completeControllerState.userResponse.vulnerability.adult.value
    );

    window.CallControllerClient.addFieldToModel(
      "Cpl_vulnerabilityChild",
      completeControllerState.userResponse.vulnerability.child.value
    );
     window.CallControllerClient.addFieldToModel(
      "Cpl_mcaAssessed",
      completeControllerState.userResponse.vulnerability.mcaAssessed.value
    );
    window.CallControllerClient.addFieldToModel(
      "Cpl_mcaRequired",
      completeControllerState.userResponse.vulnerability.mcaRequired.value
    );
    //  </vulnerability>

    window.CallControllerClient.addFieldToModel(
      "CPL_READCODES",
      completeControllerState.userResponse.readCodes.readCodesSelected
        .map(readCode => {
          return readCode.ReadCode + " - " + readCode.ReadCodeDescription;
        })
        .join(";")
    );

    // <Map BrisDoc Audit Questions>
    const brisDocAuditQuestions =
      completeControllerState.userResponse.BRISDOC_AUDIT_QUESTIONS;

    const questionAnwserArray = Object.keys(brisDocAuditQuestions.answers).map(
      key => {
        return key + "~" + brisDocAuditQuestions.answers[key];
      }
    );
    window.CallControllerClient.addFieldToModel(
      "Cpl_AuditQuestions",
      questionAnwserArray.join("^")
    );

    // Object.keys(brisDocAuditQuestions.answers).forEach(key => {
    //   const fieldName = (("Cpl_Audit_" + key) as any) as "Cpl_DynamicFields";
    //   const value = brisDocAuditQuestions.answers[key];
    //
    //   window.CallControllerClient.addFieldToModel(fieldName, value);
    // });

    // </Map BrisDoc Audit Questions>

    // <Map BrisDoc Non Clinical Questions>
    const brisDocNonClinicalQuestions =
      completeControllerState.userResponse.BRISDOC_NON_CLINICAL_AND_PRESCRIBING;

    window.CallControllerClient.addFieldToModel(
      "Cpl_medicationIssuedFromStock",
      brisDocNonClinicalQuestions.medicationIssuedFromStock
    );
    window.CallControllerClient.addFieldToModel(
      "Cpl_nonClinicalSupportToComplete",
      brisDocNonClinicalQuestions.nonClinicalSupportToCompleteCaseRequired
    );
    window.CallControllerClient.addFieldToModel(
      "Cpl_mhClinicianSignOffRequired",
      brisDocNonClinicalQuestions.mhClinicianSignOffRequired
    );
    window.CallControllerClient.addFieldToModel(
      "Cpl_supportTypeRequired",
      brisDocNonClinicalQuestions.supportTypeRequired
    );
    window.CallControllerClient.addFieldToModel(
      "Cpl_supportTypeRequiredComments",
      brisDocNonClinicalQuestions.supportTypeRequiredComments
    );
    // </Map BrisDoc Non Clinical Questions>

    // <Non_Clinical_Reason>

    window.CallControllerClient.addFieldToModel(
      "Cpl_nonClinicalReason",
      completeControllerState.userResponse.nonClinicalReason.reason.value
    );

    window.CallControllerClient.addFieldToModel(
      "Cpl_nonClinicalReasonComment",
      completeControllerState.userResponse.nonClinicalReason.comment
    );
    // </Non_Clinical_Reason>
  }

  return {
    state,

    getFollowUpInputStateDisplayText,

    careHomeSelected,
    onTelephoneChanged,
    mapLegacyUiToModel,
    launchSaveAndReturn,
    launchEndAssessment,
    cancelComplete,
    processComplete,
    setFollowUpInputState,
    saveAndReturn,
    destroy
  };
}
