import { CallSummaryService } from "@/calls/summary/call-summary-service";
import { ICleoCallSummary } from "./call-summarry-models";

const callSummaryService = new CallSummaryService();

describe("CallSummary", () => {
  it("timeHumanShort", () => {
    expect(callSummaryService.timeHumanShort(1)).toBe("<1m");
  });

  it("isRealCall", () => {
    const callSummaryMock: ICleoCallSummary = {
      AllViewExclude: null,
      AllViewInclude: null,
      CallNo: 251292307,
      CallService: {
        Id: 0,
        Description: "OOH",
        Type: ""
      },
      IucContract: {
        Id: 0,
        Description: "",
        Type: ""
      },
      cleoClientService: "Face to Face",
      CallPostCode: "unk",
      CallGenderId: 1,
      CallNhsNo: "",
      BreachActualTime: "2025-07-12T12:31:09+01:00",
      BreachWarnActualTime: "2025-07-12T12:11:09+01:00",
      BreachPreActualTime: "2025-07-12T06:31:09+01:00",
      BreachPriority: 10,
      ApplyBreach: true,
      BreachLevel1Mins: 360,
      CallWithBaseAckTime: "",
      CallUrgentYn: false,
      CallReceivedTime: "06:29",
      CallStatusValue: 1,
      IsLocked: "",
      CallForename: "Test",
      CallSurname: "NW_F2F_NON_CLINI_1",
      CallPractice: "UNKNOWN",
      Itk111Online: false,
      ChFinalDispositionCode: "Dx47",
      ChFinalDispositionDescription:
        "Refer to a community Healthcare Professional",
      FinalDispositionCode: "",
      FinalDispositionDescription: "",
      DispatchVehicle: "",
      WalkIn: false,
      CallCallback: 0,
      CallWarmTransferred: false,
      PdsTracedAndVerified: false,
      PDSAdminTrace: "",
      CallAddress1: "",
      CallAddress2: "",
      CallAddress3: "",
      CallAddress4: "",
      CallTown: "",
      CallAge: 59,
      CallAgeClass: "yrs",
      CallDobIso: "1966-06-06",
      CallCName: "",
      CallCRel: "111 referral via fax/phone/email",
      CallCreatedBy: "Nick Wall",
      CallArrivedTime: "",
      CallDoctorName: "CN=Joe Webley/O=sehnp",
      CallClassification: {
        Id: 0,
        Description: "Base"
      },
      CallSubClassification: {
        Id: 0,
        Description: ""
      },
      CallTelNo: "00000 000000",
      CallTelNoAlt1: "",
      CallTelNoAltType1: "",
      CallTelNo_R: "00000 000000",
      CallSymptoms: "",
      Call1StContact: "2025-07-12T06:33:56+01:00",
      Call1StContactPathways: "",
      PathwaysCaseId: "",
      PatientContactCode: "",
      PatientContactCodeCount: 0,
      LastFailedContactTime: "",
      CallAppointmentTime: "",
      DateAppointmentStart: "",
      CareConnectAppointmentStart: "",
      AFT_UNABLE_REASON: "",
      ComfortSentServiceTime: "",
      ComfortSmsTime: "",
      ComfortSentService2Time: "",
      ComfortSmsTime2: "",
      CourtesyTime: "",
      CourtesyUser: "",
      CourtesyCount: 0,
      CourtesyContact: false,
      CliniHighPriority: false,
      CallInformationalOutcomes: "BRI admission",
      CallInformationalSubOutcomes: "",
      CallInformationalOutcomesComment: "",
      DutyBase: "Christchurch",
      StartConsultationPerformed: true,
      Long: 0,
      Lat: 0,
      CasValidationCount: 0,
      CasValidationUser: "",
      CasValidationTime: "",
      CasValidationReason: "",
      CaseComments: "",
      LinkedCallID: "",
      COMPLETE_PREVENT: "1",
      FOLLOW_UP_URGENT: "",
      FOLLOW_UP_Active: "",
      Call_HCP: "",
      OversightValidationType: "",
      OVERSIGHT_BASE_TRIAGE_TYPE: "",
      SMS_HAS: false,
      SMS_SENT: false,
      SMS_LATEST_AT: "",
      SMS_LATEST_USER: "",
      SMS_LATEST_MESSAGE: "",
      Cpl_supportTypeRequired: "OTHER",
      GOODSAM_IMAGE_STATUS: "",
      CallFAction: "",
      Cpl_furtherActionGPText: "",
      PLS_REASON: "",
      PLS_TIME: "",
      PLS_USER: "",
      PLS_ACTION: "",
      PLS_ACTIONTEXT: ""
    };

    const result = callSummaryService.getClassification(callSummaryMock);

    expect(result).toBe("Base (Non Clinical Complete) - OTHER");
  });
});
