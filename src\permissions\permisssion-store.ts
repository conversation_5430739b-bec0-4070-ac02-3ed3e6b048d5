import { ActionContext, Module, Store } from "vuex";
import { appStore, IRootState, useStore } from "@/store/store";

import {
  CleoPermissionName,
  ICleoPermission
} from "@/permissions/permission-models";
import { PermissionService } from "@/permissions/permission-service";
import { PermissionData } from "@/permissions/permission-data";
import { CommonService } from "@/common/common-service";

const commonService: CommonService = new CommonService();
const permissionService: PermissionService = new PermissionService();
const permissionData: PermissionData = new PermissionData();

export interface IPermissionStoreState {
  isLoadingPerms: boolean;
  userAppPerms: Record<string, ICleoPermission>; //  E.g. "Call.Arrived"
  userAppPermsShort: Record<CleoPermissionName, ICleoPermission>; //  E.g. "Arrived"  ...because. 99% time we dont care about the "Call." bit.
  // applicationPermissions: {
  //   isLoading: boolean;
  //   short: Partial<Record<CleoPermissionName, ICleoPermission>>;
  // };
}

export enum PERMISSION_STORE_CONST {
  PERMISSION__CONST_MODULE_NAME = "PERMISSION__CONST_MODULE_NAME",

  //  <MUTATIONS>
  //  TODO so BADLY NAMED!!!   These are perms for a "case" not APP.  See below
  PERMISSION__MUTATION_SET_APP_PERMS_LOADING = "PERMISSION__MUTATION_SET_APP_PERMS_LOADING",
  PERMISSION__MUTATION_SET_APP_PERMS = "PERMISSION__MUTATION_SET_APP_PERMS",
  PERMISSION__MUTATION_SET_APP_PERMS_SHORT = "PERMISSION__MUTATION_SET_APP_PERMS_SHORT",

  // PERMISSION__MUTATION_SET_APPLICATION_PERMS_LOADING = "PERMISSION__MUTATION_SET_APPLICATION_PERMS_LOADING",
  // PERMISSION__MUTATION_SET_APPLICATION_PERMS_SHORT = "PERMISSION__MUTATION_SET_APPLICATION_PERMS_SHORT",
  //  </MUTATIONS>

  //  <GETTERS>
  PERMISSION__GETTER_HAS_PERM_SHORT = "PERMISSION__GETTER_HAS_PERM_SHORT",
  //  </GETTERS>

  //  <ACTIONS>
  PERMISSION__ACTIONS_GET_APP_PERMS = "PERMISSION__ACTIONS_GET_APP_PERMS",
  //  </ACTIONS>

  PERMISSION__ACTIONS_GET_APPLICATION_PERMS = "PERMISSION__ACTIONS_GET_APPLICATION_PERMS"
}

const mutations = {
  [PERMISSION_STORE_CONST.PERMISSION__MUTATION_SET_APP_PERMS_LOADING](
    state: IPermissionStoreState,
    isLoading: boolean
  ): void {
    state.isLoadingPerms = isLoading;
  },

  [PERMISSION_STORE_CONST.PERMISSION__MUTATION_SET_APP_PERMS](
    state: IPermissionStoreState,
    perms: Record<string, ICleoPermission>
  ): void {
    state.userAppPerms = commonService.simpleObjectClone(perms);
  },

  [PERMISSION_STORE_CONST.PERMISSION__MUTATION_SET_APP_PERMS_SHORT](
    state: IPermissionStoreState,
    permsShort: Record<string, ICleoPermission>
  ): void {
    state.userAppPermsShort = commonService.simpleObjectClone(permsShort);
  }
};

const getters = {
  [PERMISSION_STORE_CONST.PERMISSION__GETTER_HAS_PERM_SHORT](
    state: IPermissionStoreState
  ) {
    return (permName: string): boolean => {
      return state.userAppPermsShort[permName] ? true : false;
    };
  }
};

const actions = {
  [PERMISSION_STORE_CONST.PERMISSION__ACTIONS_GET_APP_PERMS](
    context: ActionContext<IPermissionStoreState, IRootState>,
    userRole: string
  ): void {
    context.commit(
      PERMISSION_STORE_CONST.PERMISSION__MUTATION_SET_APP_PERMS,
      {}
    );
    context.commit(
      PERMISSION_STORE_CONST.PERMISSION__MUTATION_SET_APP_PERMS_LOADING,
      true
    );
    permissionData
      .getUserPermissions(userRole)
      .then(perms => {
        context.commit(
          PERMISSION_STORE_CONST.PERMISSION__MUTATION_SET_APP_PERMS,
          perms
        );
        const permsShort = permissionService.simpleKeyPerms(perms);
        context.commit(
          PERMISSION_STORE_CONST.PERMISSION__MUTATION_SET_APP_PERMS_SHORT,
          permsShort
        );
      })
      .finally(() => {
        context.commit(
          PERMISSION_STORE_CONST.PERMISSION__MUTATION_SET_APP_PERMS_LOADING,
          false
        );
      });
  }
};

export const permissionStore: Module<IPermissionStoreState, IRootState> = {
  namespaced: true,
  state: permissionService.factoryPermissionStoreState,
  mutations,
  getters,
  actions
};

// export function usePermissionStore(): Store<IPermissionStoreState> {
//   const permStore = useStore()..state[PERMISSION_STORE_CONST.PERMISSION__CONST_MODULE_NAME].;
//   // return useStore().state[PERMISSION_STORE_CONST.PERMISSION__CONST_MODULE_NAME];
//   return permStore;
// }
