import { ICareHome } from "@/carehome/carehome-models";
import https from "@/common/https";
import { CLEO_CONFIG } from "@/common/config/config-";

import { mockCareHomesCleo } from "@/carehome/carehome-data-mock";
import * as CareHomeService from "@/carehome/carehome-service";
import { IService, SERVICE_NAME } from "@/common/services/services-models";

export function getCareHomes(service: IService): Promise<ICareHome[]> {
  return getCareHomesFromCleo(service.id > 0 ? service.id : service.name).then(
    resp => {
      return resp.map(careHomeCleo => {
        return CareHomeService.mapCareHomeCleo(careHomeCleo);
      });
    }
  );
}

export interface ICareHomeCleo {
  id: number;
  careHomeName: string;
  email: string;
  isEnabled: boolean;
}

export function getCareHomesFromCleo(
  serviceIdentifier: number | SERVICE_NAME
): Promise<ICareHomeCleo[]> {
  if (process.env.NODE_ENV === "development") {
    return Promise.resolve(mockCareHomesCleo);
  }

  return https.get(
    CLEO_CONFIG.CLEO.CALL_DB +
      "/(carehome)?openagent&action=GET_CARE_HOMES&id=" +
      serviceIdentifier,
    {
      responseType: "json"
    }
  );
}
