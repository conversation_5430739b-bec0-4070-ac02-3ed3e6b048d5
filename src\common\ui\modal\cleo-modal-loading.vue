<template>
  <transition name="modal">
    <div class="ic24-modal-mask">
      <div
        class="ic24-modal-wrapper"
        :class="isFullSizeForm ? 'e4s-modal-wrapper--full-size' : ''"
      >
        <div class="ic24-modal-container" :class="getCssClass">
          <slot name="header">
            <div class="ic24-modal-header">
              <div class="ic24-modal-header" v-text="headerMessage"></div>
            </div>
          </slot>

          <slot name="body">
            <div class="ic24-modal-body">
              <div class="ic24-modal-body">
                <LoadingSpinnerLarge></LoadingSpinnerLarge>
                <span
                  v-text="bodyMessage"
                  class="cleo-modal-loading--message "
                ></span>
              </div>
            </div>
          </slot>

          <slot name="footer">
            <div class="ic24-modal-footer" v-if="false">
              <slot name="buttons">
                <slot name="button-close-secondary">
                  <button
                    class="adapter-button adapter-width-5 adapter-button--red"
                    v-on:click.stop="$emit('closeSecondary')"
                  >
                    <span v-text="buttonSecondaryText"></span>
                  </button>
                </slot>

                <slot name="button-close-primary">
                  <button
                    class="adapter-button adapter-width-5 adapter-button--green"
                    v-on:click.stop="$emit('closePrimary')"
                  >
                    <span v-text="buttonPrimaryText"></span>
                  </button>
                </slot>
              </slot>
            </div>
          </slot>
        </div>
      </div>
    </div>
  </transition>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import LoadingSpinner from "@/common/ui/loading-spinner.vue";
import LoadingSpinnerLarge from "@/common/ui/loading-spinner-large.vue";

@Component({
  name: "cleo-modal-loading",
  components: {
    LoadingSpinnerLarge,
    LoadingSpinner
  }
})
export default class CleoModal extends Vue {
  @Prop({ default: "" }) public headerMessage!: string;
  @Prop({ default: "" }) public bodyMessage!: string;
  @Prop({ default: "Cancel" }) public buttonSecondaryText!: string;
  @Prop({ default: "OK" }) public buttonPrimaryText!: string;
  @Prop({ default: "" }) public cssClass!: string;
  @Prop({ default: false }) public isLoading!: boolean;
  @Prop({ default: false }) public isFullSizeForm!: boolean;

  public get getCssClass() {
    if (this.isFullSizeForm) {
      return "ic24-modal-container--full-size";
    }
    return this.cssClass.length === 0
      ? "ic24-modal-container-size"
      : this.cssClass;
  }

  public closeSecondary() {
    this.$emit("closeSecondary");
  }

  public closePrimary() {
    this.$emit("closePrimary");
  }
}
</script>

<style scoped>
.ic24-modal-mask {
  position: fixed;
  z-index: 9998;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: table;
  transition: opacity 0.3s ease;
}

.ic24-modal-wrapper {
  display: table-cell;
  vertical-align: middle;
}

.ic24-modal-container {
  /*width: 300px;*/
  /*width: 100%;*/
  /*height: 100%;*/
  margin: 0px auto;
  /*padding: 20px 30px;*/
  background-color: #fff;
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.33);
  transition: all 0.3s ease;
  /*font-family: Helvetica, Arial, sans-serif;*/
}

.ic24-modal-container-size {
  width: fit-content;
  max-width: 100vw;
  /*height: 50%;*/
}

.ic24-modal-container--full-size {
  width: 100% !important;
  padding: 0 !important;
  /*height: 50%;*/
}

.ic24-modal-header {
  /*font-size: 1.5rem;*/
  background: transparent
    linear-gradient(180deg, #e9effd 0%, #cbdff6 64%, #bcd5f0 65%, #bcd5f0 100%)
    0 0 no-repeat padding-box;
  padding: 5px;
}

.ic24-modal-body {
  /*margin: 20px 0;*/
  padding: 5px;
}

.ic24-modal-footer {
  padding: 5px;
}

.ic24-modal-footer button {
  /*float: right;*/
  margin-right: 5px;
}

.cleo-modal-loading--message {
  font-weight: 600;
  font-size: 1rem;
}

/*
 * The following styles are auto-applied to elements with
 * transition="modal" when their visibility is toggled
 * by Vue.js.
 *
 * You can easily play with the modal transition by editing
 * these styles.
 */

.modal-enter {
  opacity: 0;
}

.modal-leave-active {
  opacity: 0;
}

.modal-enter .modal-container,
.modal-leave-active .modal-container {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}

.ic24-modal-wrapper--full-size {
  padding: 0 !important;
}

@media only screen and (max-height: 700px) {
  .e4s-modal-container-size {
    width: 100%;
    /*height: 50%;*/
  }
}
</style>
