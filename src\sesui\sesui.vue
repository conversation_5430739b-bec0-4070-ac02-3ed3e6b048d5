<template>
  <div>
    <MountingPortal mountTo="#adapter--sesui-login-modal">
      <CleoModal v-if="telephoneCall.state.showLogin">
        <SesuiLogin
          :default-user-id="telephoneCall.state.user"
          slot="body"
          v-on:cancel="telephoneCall.cancelLogin"
          v-on:loginSesui="processLogin"
        />
        <div slot="buttons"></div>
      </CleoModal>
    </MountingPortal>

    <MountingPortal mountTo="#adapter--sesui-state"> </MountingPortal>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from "@vue/composition-api";
import { useTelephoneCall } from "@/sesui/useTelephone";
import CleoModal from "@/common/ui/modal/cleo-modal.vue";
import SesuiLogin from "@/sesui/sesui-login.vue";
import {
  CleoPermissionName,
  ICleoPermission
} from "@/permissions/permission-models";

export default defineComponent({
  name: "sesui",
  components: { SesuiLogin, CleoModal },
  props: {
    userPermissions: {
      type: Object as PropType<
        Partial<Record<CleoPermissionName, ICleoPermission>>
      >,
      default: () => {
        return {};
      }
    }
  },
  setup(props: {
    userPermissions: Partial<Record<CleoPermissionName, ICleoPermission>>;
  }) {
    const telephoneCallConfig = {
      enabled: window.MyGlobalSession.sesui.enabled,
      url: window.MyGlobalSession.sesui.url,
      user: window.MyGlobalSession.sesui.user,
      pw: window.MyGlobalSession.sesui.pw
    };

    const telephoneCall = useTelephoneCall(telephoneCallConfig);

    telephoneCall.init();

    function processLogin(userId: string) {
      telephoneCall.processLogin(userId);
    }

    return { telephoneCall, processLogin };
  }
});
</script>
